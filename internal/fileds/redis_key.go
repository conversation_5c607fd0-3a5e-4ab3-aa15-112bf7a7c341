package fileds

import "time"

const (
	AppName           = "wukong"
	Delimiter         = ":"
	PlaceholderString = "%s"
	PlaceholderInt    = "%d"
	WS                = "ws"
)

const (
	ControlLiveStreamLockTimeout = 5 * time.Second
)

const (
	WSDroneDetailPrefix  = AppName + Delimiter + WS + Delimiter + "drone_detail" + Delimiter + PlaceholderString
	WSWarnListPrefix     = AppName + Delimiter + WS + Delimiter + "warn_list"
	WSDroneMgrInfoPrefix = AppName + Delimiter + WS + Delimiter + "drone_mgr_info"
)

const (
	IsOccupiedPermissions                 = AppName + Delimiter + "permission"
	IsOccupiedVisualizationWorkPermission = AppName + Delimiter + "visualization_work_permission"
	IsDoNoRealTime                        = AppName + Delimiter + "is_do_no_real_time"
	NoRealTimeCompany                     = AppName + Delimiter + "no_real_time_company"
)

// Prefixes for drone OSD and state
const (
	DeviceBasePrefix             = AppName + Delimiter + "device_base" + Delimiter + PlaceholderString
	TaskDetailGpsPrefix          = AppName + Delimiter + "task_detail" + Delimiter + PlaceholderString + Delimiter + "gps"
	TaskDetailLogPrefix          = AppName + Delimiter + "task_detail" + Delimiter + PlaceholderString + Delimiter + "log"
	TaskDetailTakePhotoLogPrefix = AppName + Delimiter + "task_detail" + Delimiter + PlaceholderString + Delimiter + "takePhotoLog"
	TaskDetailMediaPrefix        = AppName + Delimiter + "task_detail" + Delimiter + PlaceholderString + Delimiter + "media"
	LiveReadyNoticePrefix        = AppName + Delimiter + "live_ready_notice" + Delimiter + PlaceholderString
)

// Prefix for drc and wayline
const (
	DRC_MODE_ALIVE_SECOND  = 60 * 60
	DRC_PREFIX             = AppName + Delimiter + "drc" + Delimiter + PlaceholderString
	DRC_Control_Seq_PREFIX = AppName + Delimiter + "drc_control_seq" + Delimiter + PlaceholderString
	DRC_Control_HeartBeat  = AppName + Delimiter + "drc_heartbeat_seq"
	DRC_Control_Cmd_Lock   = AppName + Delimiter + "drc_control_cmd_lock" + Delimiter + PlaceholderString
	DRC_Camera_OSD         = AppName + Delimiter + "drc_camera_osd" + Delimiter + PlaceholderString

	MQTT_ACL_PREFIX = AppName + Delimiter + "mqtt_acl" + Delimiter + PlaceholderString

	WaylineJobRunningPrefix         = AppName + Delimiter + "wayline_job_running" + Delimiter + PlaceholderString
	WaylineJobPausedPrefix          = AppName + Delimiter + "wayline_job_paused" + Delimiter + PlaceholderString
	InFlightWaylineJobRunningPrefix = AppName + Delimiter + "in_flight_wayline_job_running" + Delimiter + PlaceholderString

	MEDIA_FILE_PREFIX       = AppName + Delimiter + "media_file" + Delimiter + PlaceholderString + Delimiter + PlaceholderString
	RETURN_HOME_INFO_PREFIX = AppName + Delimiter + "return_home_info" + Delimiter + PlaceholderString + Delimiter + PlaceholderString
	DroneAttitudeInfo       = AppName + Delimiter + "drone_attitude_info" + Delimiter + PlaceholderString

	WaylineJobTransferLockPrefix = AppName + Delimiter + "wayline_job_transfer_lock" + Delimiter + PlaceholderString + Delimiter + PlaceholderString
)

// device
const (
	DEVICE_ALIVE_SECOND  = 30
	DEVICE_ONLINE_PREFIX = AppName + Delimiter + "online" + Delimiter + PlaceholderString
	OSD_PREFIX           = AppName + Delimiter + "osd" + Delimiter + PlaceholderString
)

// live
const (
	LIVE_CAPACITY                = AppName + Delimiter + "live_capacity" + Delimiter + PlaceholderString
	LiveCapacityTimestamp        = AppName + Delimiter + "live_capacity_timestamp" + Delimiter + PlaceholderString
	LIVE_STREAMING               = AppName + Delimiter + "live_streaming" + Delimiter + PlaceholderString
	Visualization_LIVE_STREAMING = AppName + Delimiter + "visualization_live_type"
	ControlLiveStreamLock        = AppName + Delimiter + "control_live_stream_lock" + Delimiter + PlaceholderString
	LiveStreamingJob             = AppName + Delimiter + "live_streaming_job" + Delimiter + PlaceholderString
	// wukong:real_time_job_data:{dockSn}
	LiveStreamingProcessingPrefix = AppName + Delimiter + "live_streaming_processing" + Delimiter
	LiveStreamingProcessing       = LiveStreamingProcessingPrefix + PlaceholderString
)

const (
	NoRealTimeJobDataLockPrefix = AppName + Delimiter + "no_real_time_job_data" + Delimiter
	NoRealTimeJobDataLock       = NoRealTimeJobDataLockPrefix + PlaceholderString

	RealTimeJobDataLockPrefix = AppName + Delimiter + "real_time_job_data" + Delimiter
	// wukong:real_time_job_data:{connId}
	RealTimeJobDataLock = RealTimeJobDataLockPrefix + PlaceholderString

	RealTimeControlLockPrefix = AppName + Delimiter + "real_time_control" + Delimiter
	// wukong:real_time_control:{connId}
	RealTimeControlLock = RealTimeControlLockPrefix + PlaceholderString
)

const (
	Coordinate = AppName + Delimiter + "coordinate" + Delimiter + PlaceholderString
)

// payload
const (
	PayloadDrc  = AppName + Delimiter + "payload_drc" + Delimiter + PlaceholderString
	SpeakerText = AppName + Delimiter + "speaker_text" + Delimiter + PlaceholderString
)

// cockpit
const (
	CreateWarnData = AppName + Delimiter + "create_warn_data" + Delimiter + PlaceholderString
)

// 智能算法巡检
const (
	AI_Control_Lock = AppName + Delimiter + "ai_control_lock" + Delimiter + PlaceholderString

	InspectionStatusPrefix = AppName + Delimiter + "inspection_status" + Delimiter
	InspectionStatus       = InspectionStatusPrefix + PlaceholderString

	ExecuteDecisionLockPrefix  = AppName + Delimiter + "execute_decision_lock" + Delimiter
	ExecuteDecisionLock        = ExecuteDecisionLockPrefix + PlaceholderString
	ExecuteDecisionLockTimeout = 5 * time.Minute

	CheckWaylineLock        = AppName + Delimiter + "check_wayline_lock" + Delimiter + PlaceholderString
	CheckWaylineLockTimeout = 1 * time.Second
)
