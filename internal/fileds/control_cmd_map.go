package fileds

import (
	"time"
	"wukong-api/cloud_sdk/cloud_api/device/cloud_enum"
	"wukong-api/cloud_sdk/cloud_api/device/request"
	live_stream_cloud_enum "wukong-api/cloud_sdk/cloud_api/live_stream/cloud_enum"
	"wukong-api/internal/dto"
	"wukong-api/internal/enum"
)

var (
	//ControlCmdMap 指令序列 key为航线id；临时存放
	ControlCmdMap = map[int][]*dto.AutoDroneControl{
		214: {
			// 4号航点：暂停航线、取消航线；接管，右移进到桥中间
			// 暂停航线
			{
				Type: enum.FlighttaskPause,
			},
			// 取消航线
			{
				Type:     enum.FlighttaskUndo,
				WaitTime: durationPtr(2 * time.Second),
			},
			// 右移
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 2,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			// 下降
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 0,
					H: -1,
					W: 0,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			// 右移
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 4,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			// 右移
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 4,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(3 * time.Second),
			},
			// 5号航点：拍照；右移出桥；下发空中航线（201）
			// 云台回中
			{
				Type: enum.PayloadCommand,
				PayloadCommandsReq: &dto.PayloadCommandsReq{
					Method: GIMBAL_RESET,
					Data: request.DronePayloadRequest{
						PayloadIndex: "99-0-0",
						ResetMode:    cloud_enum.FindGimbalResetModeEnum(cloud_enum.RECENTER.Value()),
					},
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 切变焦
			{
				Type: enum.LiveLensChange,
				LiveLensChange: &dto.LiveLensChange{
					PayloadIndex: "99-0-0",
					CameraType:   live_stream_cloud_enum.LensChangeVideo_ZOOM,
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 变焦5倍
			{
				Type: enum.PayloadCommand,
				PayloadCommandsReq: &dto.PayloadCommandsReq{
					Method: CAMERA_FOCAL_LENGTH_SET,
					Data: request.DronePayloadRequest{
						PayloadIndex: "99-0-0",
						CameraType:   cloud_enum.FindCameraTypeEnum(cloud_enum.CameraType_ZOOM.Value()),
						ZoomFactor:   floatPtr(5),
					},
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 开启探照灯
			{
				Type: enum.LightModeSet,
				PayloadLightModeSetReq: &dto.PayloadLightModeSetReq{
					Mode: intPtr(1),
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 云台调整（pitch_speed 15 * 12）
			{
				Type: enum.CameraScreenDrag,
				CameraScreenDrag: &dto.CameraScreenDrag{
					PayloadIndex: "99-0-0",
					Locked:       boolPtr(false),
					PitchSpeed:   floatPtr(15),
					YawSpeed:     floatPtr(0),
					DoNumber:     12,
					WaitTime:     durationPtr(300 * time.Millisecond),
				},
				WaitTime: durationPtr(2 * time.Second),
			},
			// 拍照
			{
				Type: enum.PayloadCommand,
				PayloadCommandsReq: &dto.PayloadCommandsReq{
					Method: CAMERA_PHOTO_TAKE,
					Data: request.DronePayloadRequest{
						PayloadIndex: "99-0-0",
					},
				},
				WaitTime: durationPtr(2 * time.Second),
			},
			// 关闭探照灯
			{
				Type: enum.LightModeSet,
				PayloadLightModeSetReq: &dto.PayloadLightModeSetReq{
					Mode: intPtr(0),
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 切广角
			{
				Type: enum.LiveLensChange,
				LiveLensChange: &dto.LiveLensChange{
					PayloadIndex: "99-0-0",
					CameraType:   live_stream_cloud_enum.LensChangeVideo_WIDE,
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 云台回中
			{
				Type: enum.PayloadCommand,
				PayloadCommandsReq: &dto.PayloadCommandsReq{
					Method: GIMBAL_RESET,
					Data: request.DronePayloadRequest{
						PayloadIndex: "99-0-0",
						ResetMode:    cloud_enum.FindGimbalResetModeEnum(cloud_enum.RECENTER.Value()),
					},
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 右移
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 4,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			// 右移
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 4,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			// 右移
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 4,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			// 右移
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 4,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			// 下发空中航线（215）
			{
				Type: enum.InFlightWaylineDeliver,
				InFlightWaylineDeliver: &dto.InFlightWaylineDeliver{
					WaylineManageID: 215,
					RthAltitude:     50,
				},
			},
		},
		215: {
			// 9号航点：左移进到桥中间
			//左移
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: -2,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			// 左移
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: -4,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			// 左移
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: -4,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(3 * time.Second),
			},
			// 10号航点：拍照；无人机偏航角转动到-90度；拍照；左移出桥
			// 切变焦
			{
				Type: enum.LiveLensChange,
				LiveLensChange: &dto.LiveLensChange{
					PayloadIndex: "99-0-0",
					CameraType:   live_stream_cloud_enum.LensChangeVideo_ZOOM,
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 变焦7倍
			{
				Type: enum.PayloadCommand,
				PayloadCommandsReq: &dto.PayloadCommandsReq{
					Method: CAMERA_FOCAL_LENGTH_SET,
					Data: request.DronePayloadRequest{
						PayloadIndex: "99-0-0",
						CameraType:   cloud_enum.FindCameraTypeEnum(cloud_enum.CameraType_ZOOM.Value()),
						ZoomFactor:   floatPtr(7),
					},
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 开启探照灯
			{
				Type: enum.LightModeSet,
				PayloadLightModeSetReq: &dto.PayloadLightModeSetReq{
					Mode: intPtr(1),
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 云台调整pitch_speed 15 * 6
			{
				Type: enum.CameraScreenDrag,
				CameraScreenDrag: &dto.CameraScreenDrag{
					PayloadIndex: "99-0-0",
					Locked:       boolPtr(false),
					PitchSpeed:   floatPtr(15),
					YawSpeed:     floatPtr(0),
					DoNumber:     6,
					WaitTime:     durationPtr(300 * time.Millisecond),
				},
				WaitTime: durationPtr(2 * time.Second),
			},
			// 云台调整yaw_speed 15 * 4
			{
				Type: enum.CameraScreenDrag,
				CameraScreenDrag: &dto.CameraScreenDrag{
					PayloadIndex: "99-0-0",
					Locked:       boolPtr(false),
					PitchSpeed:   floatPtr(0),
					YawSpeed:     floatPtr(15),
					DoNumber:     4,
					WaitTime:     durationPtr(300 * time.Millisecond),
				},
				WaitTime: durationPtr(2 * time.Second),
			},
			// 拍照
			{
				Type: enum.PayloadCommand,
				PayloadCommandsReq: &dto.PayloadCommandsReq{
					Method: CAMERA_PHOTO_TAKE,
					Data: request.DronePayloadRequest{
						PayloadIndex: "99-0-0",
					},
				},
				WaitTime: durationPtr(2 * time.Second),
			},
			// 关闭探照灯
			{
				Type: enum.LightModeSet,
				PayloadLightModeSetReq: &dto.PayloadLightModeSetReq{
					Mode: intPtr(0),
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 无人机偏航角转动到-90度
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 0,
					H: 0,
					W: -30,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 0,
					H: 0,
					W: -90,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 0,
					H: 0,
					W: -90,
				},
				WaitTime: durationPtr(3 * time.Second),
			},
			// 开启探照灯
			{
				Type: enum.LightModeSet,
				PayloadLightModeSetReq: &dto.PayloadLightModeSetReq{
					Mode: intPtr(1),
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 云台调整（ yaw_speed -15 * 15）
			{
				Type: enum.CameraScreenDrag,
				CameraScreenDrag: &dto.CameraScreenDrag{
					PayloadIndex: "99-0-0",
					Locked:       boolPtr(false),
					PitchSpeed:   floatPtr(0),
					YawSpeed:     floatPtr(-15),
					DoNumber:     15,
					WaitTime:     durationPtr(300 * time.Millisecond),
				},
				WaitTime: durationPtr(2 * time.Second),
			},
			// 拍照
			{
				Type: enum.PayloadCommand,
				PayloadCommandsReq: &dto.PayloadCommandsReq{
					Method: CAMERA_PHOTO_TAKE,
					Data: request.DronePayloadRequest{
						PayloadIndex: "99-0-0",
					},
				},
				WaitTime: durationPtr(2 * time.Second),
			},
			// 关闭探照灯
			{
				Type: enum.LightModeSet,
				PayloadLightModeSetReq: &dto.PayloadLightModeSetReq{
					Mode: intPtr(0),
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 切广角
			{
				Type: enum.LiveLensChange,
				LiveLensChange: &dto.LiveLensChange{
					PayloadIndex: "99-0-0",
					CameraType:   live_stream_cloud_enum.LensChangeVideo_WIDE,
				},
				WaitTime: durationPtr(200 * time.Millisecond),
			},
			// 云台回中
			{
				Type: enum.PayloadCommand,
				PayloadCommandsReq: &dto.PayloadCommandsReq{
					Method: GIMBAL_RESET,
					Data: request.DronePayloadRequest{
						PayloadIndex: "99-0-0",
						ResetMode:    cloud_enum.FindGimbalResetModeEnum(cloud_enum.RECENTER.Value()),
					},
				},
				WaitTime: durationPtr(200 * time.Millisecond),
			},
			// 左移出桥
			// 左移
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: -4,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			// 左移
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: -4,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			// 左移
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: -4,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			// 左移
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: -4,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(5 * time.Second),
			},
			// 11号航点：下发空中航线（216）
			// 下发空中航线（216）
			{
				Type: enum.InFlightWaylineDeliver,
				InFlightWaylineDeliver: &dto.InFlightWaylineDeliver{
					WaylineManageID: 216,
					RthAltitude:     50,
				},
			},
		},
		216: {
			// 12号航点：右移进到桥中间
			// 下降
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 0,
					H: -1,
					W: 0,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			// 右移
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 2,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 4,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(3 * time.Second),
			},
			// 13号航点：拍照；右移出桥
			// 切变焦
			{
				Type: enum.LiveLensChange,
				LiveLensChange: &dto.LiveLensChange{
					PayloadIndex: "99-0-0",
					CameraType:   live_stream_cloud_enum.LensChangeVideo_ZOOM,
				},
				WaitTime: durationPtr(200 * time.Millisecond),
			},
			// 变焦5倍
			{
				Type: enum.PayloadCommand,
				PayloadCommandsReq: &dto.PayloadCommandsReq{
					Method: CAMERA_FOCAL_LENGTH_SET,
					Data: request.DronePayloadRequest{
						PayloadIndex: "99-0-0",
						CameraType:   cloud_enum.FindCameraTypeEnum(cloud_enum.CameraType_ZOOM.Value()),
						ZoomFactor:   floatPtr(5),
					},
				},
				WaitTime: durationPtr(200 * time.Millisecond),
			},
			// 开启探照灯
			{
				Type: enum.LightModeSet,
				PayloadLightModeSetReq: &dto.PayloadLightModeSetReq{
					Mode: intPtr(1),
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 云台调整（pitch_speed 15 * 18）
			{
				Type: enum.CameraScreenDrag,
				CameraScreenDrag: &dto.CameraScreenDrag{
					PayloadIndex: "99-0-0",
					Locked:       boolPtr(false),
					PitchSpeed:   floatPtr(15),
					YawSpeed:     floatPtr(0),
					DoNumber:     18,
					//WaitTime:     durationPtr(300 * time.Millisecond),
					WaitTime: durationPtr(500 * time.Millisecond),
				},
				WaitTime: durationPtr(2 * time.Second),
			},
			// 云台调整（ yaw_speed -15 * 8）
			{
				Type: enum.CameraScreenDrag,
				CameraScreenDrag: &dto.CameraScreenDrag{
					PayloadIndex: "99-0-0",
					Locked:       boolPtr(false),
					PitchSpeed:   floatPtr(0),
					YawSpeed:     floatPtr(-15),
					DoNumber:     8,
					WaitTime:     durationPtr(300 * time.Millisecond),
				},
				WaitTime: durationPtr(2 * time.Second),
			},
			// 拍照
			{
				Type: enum.PayloadCommand,
				PayloadCommandsReq: &dto.PayloadCommandsReq{
					Method: CAMERA_PHOTO_TAKE,
					Data: request.DronePayloadRequest{
						PayloadIndex: "99-0-0",
					},
				},
				WaitTime: durationPtr(2 * time.Second),
			},
			// 关闭探照灯
			{
				Type: enum.LightModeSet,
				PayloadLightModeSetReq: &dto.PayloadLightModeSetReq{
					Mode: intPtr(0),
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 切广角
			{
				Type: enum.LiveLensChange,
				LiveLensChange: &dto.LiveLensChange{
					PayloadIndex: "99-0-0",
					CameraType:   live_stream_cloud_enum.LensChangeVideo_WIDE,
				},
				WaitTime: durationPtr(200 * time.Millisecond),
			},
			// 云台回中
			{
				Type: enum.PayloadCommand,
				PayloadCommandsReq: &dto.PayloadCommandsReq{
					Method: GIMBAL_RESET,
					Data: request.DronePayloadRequest{
						PayloadIndex: "99-0-0",
						ResetMode:    cloud_enum.FindGimbalResetModeEnum(cloud_enum.RECENTER.Value()),
					},
				},
				WaitTime: durationPtr(200 * time.Millisecond),
			},
			// 右移
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 4,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			// 右移
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 4,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			// 右移
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 6,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(5 * time.Second),
			},
			// 14号航点：执行下个航线
			// 下发空中航线（220）
			{
				Type: enum.InFlightWaylineDeliver,
				InFlightWaylineDeliver: &dto.InFlightWaylineDeliver{
					WaylineManageID: 220,
					RthAltitude:     50,
				},
			},
		},
		268: {
			// 暂停航线
			{
				Type: enum.FlighttaskPause,
			},
			// 取消航线
			{
				Type:     enum.FlighttaskUndo,
				WaitTime: durationPtr(2 * time.Second),
			},
			// 穿桥
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 3,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 3.5,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(1 * time.Second),
			},
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 0,
					H: 1,
					W: 0,
				},
				WaitTime: durationPtr(2 * time.Second),
			},
			// 云台回中
			{
				Type: enum.PayloadCommand,
				PayloadCommandsReq: &dto.PayloadCommandsReq{
					Method: GIMBAL_RESET,
					Data: request.DronePayloadRequest{
						PayloadIndex: "99-0-0",
						ResetMode:    cloud_enum.FindGimbalResetModeEnum(cloud_enum.RECENTER.Value()),
					},
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 切变焦
			{
				Type: enum.LiveLensChange,
				LiveLensChange: &dto.LiveLensChange{
					PayloadIndex: "99-0-0",
					CameraType:   live_stream_cloud_enum.LensChangeVideo_ZOOM,
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 变焦2倍
			{
				Type: enum.PayloadCommand,
				PayloadCommandsReq: &dto.PayloadCommandsReq{
					Method: CAMERA_FOCAL_LENGTH_SET,
					Data: request.DronePayloadRequest{
						PayloadIndex: "99-0-0",
						CameraType:   cloud_enum.FindCameraTypeEnum(cloud_enum.CameraType_ZOOM.Value()),
						ZoomFactor:   floatPtr(2),
					},
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 开启探照灯
			{
				Type: enum.LightModeSet,
				PayloadLightModeSetReq: &dto.PayloadLightModeSetReq{
					Mode: intPtr(1),
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 云台调整（pitch_speed 15 * 7）
			{
				Type: enum.CameraScreenDrag,
				CameraScreenDrag: &dto.CameraScreenDrag{
					PayloadIndex: "99-0-0",
					Locked:       boolPtr(false),
					PitchSpeed:   floatPtr(15),
					YawSpeed:     floatPtr(0),
					DoNumber:     7,
					WaitTime:     durationPtr(300 * time.Millisecond),
				},
				WaitTime: durationPtr(2 * time.Second),
			},
			// 拍照
			{
				Type: enum.PayloadCommand,
				PayloadCommandsReq: &dto.PayloadCommandsReq{
					Method: CAMERA_PHOTO_TAKE,
					Data: request.DronePayloadRequest{
						PayloadIndex: "99-0-0",
					},
				},
				WaitTime: durationPtr(2 * time.Second),
			},
			// 关闭探照灯
			{
				Type: enum.LightModeSet,
				PayloadLightModeSetReq: &dto.PayloadLightModeSetReq{
					Mode: intPtr(0),
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 切广角
			{
				Type: enum.LiveLensChange,
				LiveLensChange: &dto.LiveLensChange{
					PayloadIndex: "99-0-0",
					CameraType:   live_stream_cloud_enum.LensChangeVideo_WIDE,
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 云台回中
			{
				Type: enum.PayloadCommand,
				PayloadCommandsReq: &dto.PayloadCommandsReq{
					Method: GIMBAL_RESET,
					Data: request.DronePayloadRequest{
						PayloadIndex: "99-0-0",
						ResetMode:    cloud_enum.FindGimbalResetModeEnum(cloud_enum.RECENTER.Value()),
					},
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 出桥
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 4,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 4,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(2 * time.Second),
			},
			// 下发空中航线（264）
			{
				Type: enum.InFlightWaylineDeliver,
				InFlightWaylineDeliver: &dto.InFlightWaylineDeliver{
					WaylineManageID: 264,
					RthAltitude:     50,
				},
			},
		},
		264: {
			// 穿桥
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 2,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 2,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 0,
					H: 1,
					W: 0,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 4,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(2 * time.Second),
			},
			// 云台回中
			{
				Type: enum.PayloadCommand,
				PayloadCommandsReq: &dto.PayloadCommandsReq{
					Method: GIMBAL_RESET,
					Data: request.DronePayloadRequest{
						PayloadIndex: "99-0-0",
						ResetMode:    cloud_enum.FindGimbalResetModeEnum(cloud_enum.RECENTER.Value()),
					},
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 切变焦
			{
				Type: enum.LiveLensChange,
				LiveLensChange: &dto.LiveLensChange{
					PayloadIndex: "99-0-0",
					CameraType:   live_stream_cloud_enum.LensChangeVideo_ZOOM,
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 变焦2倍
			{
				Type: enum.PayloadCommand,
				PayloadCommandsReq: &dto.PayloadCommandsReq{
					Method: CAMERA_FOCAL_LENGTH_SET,
					Data: request.DronePayloadRequest{
						PayloadIndex: "99-0-0",
						CameraType:   cloud_enum.FindCameraTypeEnum(cloud_enum.CameraType_ZOOM.Value()),
						ZoomFactor:   floatPtr(2),
					},
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 开启探照灯
			{
				Type: enum.LightModeSet,
				PayloadLightModeSetReq: &dto.PayloadLightModeSetReq{
					Mode: intPtr(1),
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 云台调整
			// 云台调整（pitch_speed 15 * 7）
			{
				Type: enum.CameraScreenDrag,
				CameraScreenDrag: &dto.CameraScreenDrag{
					PayloadIndex: "99-0-0",
					Locked:       boolPtr(false),
					PitchSpeed:   floatPtr(15),
					YawSpeed:     floatPtr(0),
					DoNumber:     7,
					WaitTime:     durationPtr(300 * time.Millisecond),
				},
				WaitTime: durationPtr(2 * time.Second),
			},
			// 拍照
			{
				Type: enum.PayloadCommand,
				PayloadCommandsReq: &dto.PayloadCommandsReq{
					Method: CAMERA_PHOTO_TAKE,
					Data: request.DronePayloadRequest{
						PayloadIndex: "99-0-0",
					},
				},
				WaitTime: durationPtr(2 * time.Second),
			},
			// 关闭探照灯
			{
				Type: enum.LightModeSet,
				PayloadLightModeSetReq: &dto.PayloadLightModeSetReq{
					Mode: intPtr(0),
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 切广角
			{
				Type: enum.LiveLensChange,
				LiveLensChange: &dto.LiveLensChange{
					PayloadIndex: "99-0-0",
					CameraType:   live_stream_cloud_enum.LensChangeVideo_WIDE,
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 云台回中
			{
				Type: enum.PayloadCommand,
				PayloadCommandsReq: &dto.PayloadCommandsReq{
					Method: GIMBAL_RESET,
					Data: request.DronePayloadRequest{
						PayloadIndex: "99-0-0",
						ResetMode:    cloud_enum.FindGimbalResetModeEnum(cloud_enum.RECENTER.Value()),
					},
				},
				WaitTime: durationPtr(300 * time.Millisecond),
			},
			// 出桥
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 4,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 4,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			{
				Type: enum.DroneControl,
				DroneControlReq: &dto.DroneControlReq{
					X: 0,
					Y: 4,
					H: 0,
					W: 0,
				},
				WaitTime: durationPtr(800 * time.Millisecond),
			},
			// 下发空中航线返航（262）
			{
				Type: enum.InFlightWaylineDeliver,
				InFlightWaylineDeliver: &dto.InFlightWaylineDeliver{
					WaylineManageID: 262,
					RthAltitude:     50,
				},
			},
		},
	}
)

func durationPtr(v time.Duration) *time.Duration {
	return &v
}

func intPtr(v int) *int {
	return &v
}

func floatPtr(v float64) *float64 {
	return &v
}

func boolPtr(v bool) *bool {
	return &v
}
