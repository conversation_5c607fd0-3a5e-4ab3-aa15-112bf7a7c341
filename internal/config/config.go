package config

import (
	"path"
	"runtime"
	"sync"

	"github.com/mitchellh/mapstructure"
	"github.com/spf13/viper"
)

const (
	RunEnvDev   = "develop"
	RunEnvProd  = "product"
	RunEnvLocal = "local"
)

type Config struct {
	RunEnv     string `json:"run_env"`
	ClusterEnv string `json:"cluster"`
	Database   struct {
		Default      string `json:"default"`
		MaxIdleConns int    `json:"max_idle_conns"`
		MaxOpenConns int    `json:"max_open_conns"`
	} `json:"database"`
	DatabaseCloudSample struct {
		Default string `json:"default"`
	} `json:"database_cloud_sample"`
	Cos struct {
		AppId     string `json:"app_id"`
		SecretId  string `json:"secret_id"`
		SecretKey string `json:"secret_key"`
		Region    string `json:"region"`
		Bucket    string `json:"bucket"`
		CDN       string `json:"cdn"`
		DoMain    string `json:"domain"`
	} `json:"cos"`
	Oss struct {
		AccessKey       string `json:"access-key"`
		SecretKey       string `json:"secret-key"`
		Region          string `json:"region"`
		Bucket          string `json:"bucket"`
		DoMain          string `json:"domain"`
		Endpoint        string `json:"endpoint"`
		RoleSessionName string `json:"role-session-name"`
		RoleArn         string `json:"role-arn"`
		ObjectDirPrefix string `json:"object-dir-prefix"`
		Expire          int    `json:"expire"`
	} `json:"oss"`
	Redis struct {
		Address  string `yaml:"address"`
		Password string `yaml:"password"`
		Db       int    `yaml:"db"`
	} `json:"redis"`
	Mqtt struct {
		Basic struct {
			Broker   string `json:"broker"`
			Port     int    `json:"port"`
			User     string `json:"user"`
			Password string `json:"password"`
			ClientId string `json:"client_id"`
		} `json:"basic"`
		Drc struct {
			Broker   string `json:"broker"`
			Port     int    `json:"port"`
			User     string `json:"user"`
			Password string `json:"password"`
			ClientId string `json:"client_id"`
		} `json:"drc"`
	} `json:"mqtt"`
	Predict struct {
		Address string `json:"address"`
	} `json:"predict"`
	Helper struct {
		Address string `json:"address"`
	} `json:"helper"`
	BaiduMap struct {
		AK  string `json:"ak"`
		Uri string `json:"uri"`
	} `json:"baidu_map"`
	Live struct {
		Ali struct {
			FlvURL         string `json:"flv_url"`
			PullSecret     string `json:"pull_secret"`
			RtmpURL        string `json:"rtmp_url"`
			RtmpPushSecret string `json:"rtmp_push_secret"`
		} `json:"ali"`
		Srs struct {
			RtmpURL string `json:"rtmp_url"`
			FlvURL  string `json:"flv_url"`
		} `json:"srs"`
		//SrsDock struct {
		//	RtmpURL        string `json:"rtmp_url"`
		//	RtmpPushSecret string `json:"rtmp_push_secret"`
		//	FlvURL         string `json:"flv_url"`
		//} `json:"srs_dock"`
	} `json:"live"`
	Sms struct {
		ApId      string `json:"ap_id"`
		SecretKey string `json:"secret_key"`
		EcName    string `json:"ec_name"`
		Sign      string `json:"sign"`
		AddSerial string `json:"add_serial"`
		Url       string `json:"url"`
	} `json:"sms"`
	AI struct {
		ApiKey string `json:"api_key"`
	} `json:"ai"`
	GRPC struct {
		Port string `json:"port"`
	} `json:"grpc"`
}

var (
	conf    Config
	confOne sync.Once
)

func getCurrentAbPathByCaller() string {
	var abPath string
	_, filename, _, ok := runtime.Caller(0)
	if ok {
		abPath = path.Dir(filename)
	}
	return abPath
}

func mustLoadConfig() {
	viper.AutomaticEnv()
	conf.RunEnv = viper.GetString("RUN_ENV")
	conf.ClusterEnv = viper.GetString("CLUSTER_NAME")

	dir := path.Join(path.Dir(path.Dir(getCurrentAbPathByCaller())), "config")
	//dir := path.Join(".", "config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(dir)

	switch conf.RunEnv {
	case RunEnvProd:
		viper.SetConfigName("product.yaml")
	case RunEnvDev:
		viper.SetConfigName("develop.yaml")
	case RunEnvLocal:
		viper.SetConfigName("local.yaml")
	default:
		viper.SetConfigName("local.yaml")
	}

	err := viper.ReadInConfig()
	if err != nil {
		panic(err)
	}

	err = viper.Unmarshal(&conf, func(configName *mapstructure.DecoderConfig) { configName.TagName = "json" })
	if err != nil {
		panic(err)
	}
}

func GetConfig() Config {
	confOne.Do(func() { mustLoadConfig() })
	return conf
}
