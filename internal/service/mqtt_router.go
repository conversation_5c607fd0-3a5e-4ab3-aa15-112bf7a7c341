package service

import (
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	control_request "wukong-api/cloud_sdk/cloud_api/control/request"
	debug_response "wukong-api/cloud_sdk/cloud_api/debug/response"
	"wukong-api/cloud_sdk/cloud_api/device/cloud_enum"
	device_request "wukong-api/cloud_sdk/cloud_api/device/request"
	hms_request "wukong-api/cloud_sdk/cloud_api/hms"
	media_request "wukong-api/cloud_sdk/cloud_api/media/request"
	psdk_request "wukong-api/cloud_sdk/cloud_api/psdk/request"
	remote_control_request "wukong-api/cloud_sdk/cloud_api/remote_control/request"
	wayline_request "wukong-api/cloud_sdk/cloud_api/wayline/request"
	"wukong-api/cloud_sdk/mqtt"
	"wukong-api/internal/cloudinfra"
	"wukong-api/internal/fileds"
	"wukong-api/internal/repo"

	"github.com/thoas/go-funk"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	routerServiceOnce sync.Once
	routerService     *RouterService
)

type RouterService struct {
	db     *gorm.DB
	logger *zap.SugaredLogger
}

func NewMqttRouterService() *RouterService {
	routerServiceOnce.Do(func() {
		routerService = &RouterService{
			db:     repo.GetDatabase(),
			logger: repo.GetLogger(),
		}
	})
	return routerService
}

// DispatchMessage 处理上云topic的路由方法
func (s *RouterService) DispatchMessage(topic string, payload []byte) error {
	values, err := parseVar(topic)
	if err != nil {
		return err
	}

	deviceSn := values[2]
	if values[3] != "osd" {
		s.logger.Debugf("DispatchMessage: topic: %s, payload: %s", topic, string(payload))
	}

	if values[0] == "thing" && values[1] == "product" {
		switch values[3] {
		case "state":
			if err = s.handleState(deviceSn, payload); err != nil {
				return err
			}
		case "osd":
			if err = s.handleOsd(deviceSn, payload); err != nil {
				return err
			}
		case "requests":
			if err = s.handleRequests(payload); err != nil {
				return err
			}
		case "events":
			if err = s.handleEvents(payload); err != nil {
				return err
			}
		case "services_reply":
			if err = s.handelServicesReply(payload); err != nil {
				return err
			}
		case "drc":
			if err = s.handelDrcUp(deviceSn, payload); err != nil {
				return err
			}
		}
	}

	return nil
}

func parseVar(topic string) ([]string, error) {
	topicSplit := strings.Split(topic, "/")
	if len(topicSplit) < 4 {
		return []string{}, fmt.Errorf(fmt.Sprintf("topic中缺少变量, topic: %s", topic))
	}
	return topicSplit, nil
}

func (s *RouterService) handleOsd(deviceSn string, payload []byte) error {
	var osdData mqtt.TopicOsdRequest[json.RawMessage]
	err := json.Unmarshal(payload, &osdData)
	if err != nil {
		s.logger.Errorf("handleOsd: failed to decode payload: %s", err.Error())
		return err
	}

	deviceSvc := NewDeviceService()

	osdDevice, _ := deviceSvc.GetDeviceBySn(deviceSn)
	if osdDevice == nil {
		return nil
	}

	switch osdDevice.Domain {
	case cloud_enum.SubDevice.Value():
		if err = deviceSvc.OsdDroneHandle(osdData, osdDevice); err != nil {
			return err
		}

		if err = NewDroneMsgService().HandleDroneOSD(deviceSn, payload); err != nil {
			return err
		}
	case cloud_enum.Dock.Value():
		var data device_request.OsdDock
		if err = json.Unmarshal(osdData.Data, &data); err != nil {
			s.logger.Errorf("handleOsd: gateway: %s, failed to decode Data: %s", osdData.Gateway, err.Error())
			return err
		}

		params := mqtt.TopicOsdRequest[device_request.OsdDock]{
			Tid:       osdData.Tid,
			Bid:       osdData.Bid,
			Timestamp: osdData.Timestamp,
			Data:      data,
			Gateway:   osdData.Gateway,
		}

		if err = deviceSvc.OsdDockHandle(params, osdDevice, payload); err != nil {
			return err
		}
	case cloud_enum.Gateway.Value():
		var data device_request.OsdRC
		if err = json.Unmarshal(osdData.Data, &data); err != nil {
			s.logger.Errorf("handleOsd: gateway: %s, failed to decode Data: %s", osdData.Gateway, err.Error())
			return err
		}

		params := mqtt.TopicOsdRequest[device_request.OsdRC]{
			Tid:       osdData.Tid,
			Bid:       osdData.Bid,
			Timestamp: osdData.Timestamp,
			Data:      data,
			Gateway:   osdData.Gateway,
		}

		if err = deviceSvc.OsdRCHandle(params, osdDevice); err != nil {
			return err
		}
	}

	return nil
}

func (s *RouterService) handleState(deviceSn string, payload []byte) error {
	var stateData mqtt.TopicStateRequest[json.RawMessage]
	err := json.Unmarshal(payload, &stateData)
	if err != nil {
		s.logger.Errorf("handleState: failed to decode payload: %s", err.Error())
		return err
	}

	stateDevice, _ := NewDeviceService().GetDeviceBySn(deviceSn)
	if stateDevice == nil {
		return nil
	}

	switch stateDevice.Domain {
	case cloud_enum.SubDevice.Value():
		var data device_request.StateDrone
		if err = json.Unmarshal(stateData.Data, &data); err != nil {
			s.logger.Errorf("handleState: gateway: %s, failed to decode Data: %s", stateData.Gateway, err.Error())
			return err
		}

		params := mqtt.TopicStateRequest[device_request.StateDrone]{
			Tid:       stateData.Tid,
			Bid:       stateData.Bid,
			Timestamp: stateData.Timestamp,
			Data:      data,
			Gateway:   stateData.Gateway,
			NeedReply: stateData.NeedReply,
		}

		if err = NewDeviceService().StateDroneHandle(params); err != nil {
			return err
		}
	case cloud_enum.Dock.Value():
		var data device_request.StateDock
		if err = json.Unmarshal(stateData.Data, &data); err != nil {
			s.logger.Errorf("handleState: gateway: %s, failed to decode Data: %s", stateData.Gateway, err.Error())
			return err
		}

		params := mqtt.TopicStateRequest[device_request.StateDock]{
			Tid:       stateData.Tid,
			Bid:       stateData.Bid,
			Timestamp: stateData.Timestamp,
			Data:      data,
			Gateway:   stateData.Gateway,
			NeedReply: stateData.NeedReply,
		}

		if err = NewDeviceService().StateDockHandle(params, string(payload)); err != nil {
			return err
		}
	case cloud_enum.Gateway.Value():
		var data device_request.StateRC
		if err = json.Unmarshal(stateData.Data, &data); err != nil {
			s.logger.Errorf("handleState: gateway: %s, failed to decode Data: %s", stateData.Gateway, err.Error())
			return err
		}

		if data.LiveStatus != nil {
			s.logger.Infof("LiveStatus: %s", payload)
		}

		params := mqtt.TopicStateRequest[device_request.StateRC]{
			Tid:       stateData.Tid,
			Bid:       stateData.Bid,
			Timestamp: stateData.Timestamp,
			Data:      data,
			Gateway:   stateData.Gateway,
			NeedReply: stateData.NeedReply,
		}

		if err = NewDeviceService().StateRcHandle(params); err != nil {
			return err
		}
	}

	return nil
}

func (s *RouterService) handleRequests(payload []byte) error {
	var requestsData mqtt.TopicRequestsRequest[json.RawMessage]
	err := json.Unmarshal(payload, &requestsData)
	if err != nil {
		s.logger.Errorf("handleRequests: failed to decode payload: %s", err.Error())
		return err
	}

	switch requestsData.Method {
	case fileds.FLIGHTTASK_RESOURCE_GET:
		var data wayline_request.FlightTaskResourceGetRequest
		if err = json.Unmarshal(requestsData.Data, &data); err != nil {
			s.logger.Errorf("handleRequests: failed to decode Data: %s", err.Error())
			return err
		}

		params := mqtt.TopicRequestsRequest[wayline_request.FlightTaskResourceGetRequest]{
			Tid:       requestsData.Tid,
			Bid:       requestsData.Bid,
			Timestamp: requestsData.Timestamp,
			Data:      data,
			Method:    requestsData.Method,
			Gateway:   requestsData.Gateway,
		}

		if err = NewFightTaskService().flightTaskResourceGetHandle(params); err != nil {
			return err
		}
	case fileds.FLIGHT_AREAS_GET:
		params := mqtt.TopicRequestsRequest[any]{
			Tid:       requestsData.Tid,
			Bid:       requestsData.Bid,
			Timestamp: requestsData.Timestamp,
			Data:      nil,
			Method:    requestsData.Method,
			Gateway:   requestsData.Gateway,
		}

		if err = NewFightAreaService().FlightAreaGetHandle(params); err != nil {
			return err
		}
	case fileds.OFFLINE_MAP_GET:
		params := mqtt.TopicRequestsRequest[any]{
			Tid:       requestsData.Tid,
			Bid:       requestsData.Bid,
			Timestamp: requestsData.Timestamp,
			Data:      nil,
			Method:    requestsData.Method,
			Gateway:   requestsData.Gateway,
		}

		if err = NewFightMapService().OfflineMapGetHandle(params); err != nil {
			return err
		}
	}

	return nil
}

func (s *RouterService) handleEvents(payload []byte) error {
	var eventsData mqtt.TopicEventsRequest[json.RawMessage]
	err := json.Unmarshal(payload, &eventsData)
	if err != nil {
		s.logger.Errorf("handleEvents: failed to decode payload: %s", err.Error())
		return err
	}

	switch eventsData.Method {
	case fileds.FLIGHTTASK_PROGRESS:
		var data mqtt.EventsDataRequest[wayline_request.FlightTaskProgress]
		if err = json.Unmarshal(eventsData.Data, &data); err != nil {
			s.logger.Errorf("handleEvents: method: %s, failed to decode Data: %s", eventsData.Method, err.Error())
			return err
		}

		params := mqtt.TopicEventsRequest[mqtt.EventsDataRequest[wayline_request.FlightTaskProgress]]{
			Tid:       eventsData.Tid,
			Bid:       eventsData.Bid,
			Timestamp: eventsData.Timestamp,
			Data:      data,
			Method:    eventsData.Method,
			Gateway:   eventsData.Gateway,
			NeedReply: eventsData.NeedReply,
		}

		if err = NewFightTaskService().flightTaskProgressHandle(params); err != nil {
			s.logger.Errorf("handleFlighttaskProgress: failed to handle flight task progress, gateway: %s, bid: %s, err: %v", eventsData.Gateway, eventsData.Bid, err)
			return err
		}
	case fileds.RETURN_HOME_INFO:
		var data wayline_request.ReturnHomeInfo
		if err = json.Unmarshal(eventsData.Data, &data); err != nil {
			s.logger.Errorf("handleEvents: method: %s, failed to decode Data: %s", eventsData.Method, err.Error())
			return err
		}

		params := mqtt.TopicEventsRequest[wayline_request.ReturnHomeInfo]{
			Tid:       eventsData.Tid,
			Bid:       eventsData.Bid,
			Timestamp: eventsData.Timestamp,
			Data:      data,
			Method:    eventsData.Method,
			Gateway:   eventsData.Gateway,
			NeedReply: eventsData.NeedReply,
		}

		if err = NewFightTaskService().returnHomeInfoHandle(params); err != nil {
			return err
		}
	case fileds.EXIT_HOMING_NOTIFY:
		var data wayline_request.ExitHomingNotify
		if err = json.Unmarshal(eventsData.Data, &data); err != nil {
			s.logger.Errorf("handleEvents: method: %s, failed to decode Data: %s", eventsData.Method, err.Error())
			return err
		}

		params := mqtt.TopicEventsRequest[wayline_request.ExitHomingNotify]{
			Tid:       eventsData.Tid,
			Bid:       eventsData.Bid,
			Timestamp: eventsData.Timestamp,
			Data:      data,
			Method:    eventsData.Method,
			Gateway:   eventsData.Gateway,
			NeedReply: eventsData.NeedReply,
		}

		if err = NewFightTaskService().exitHomingNotifyHandle(params); err != nil {
			return err
		}
	case fileds.FILE_UPLOAD_CALLBACK:
		var data media_request.FileUploadCallback
		if err = json.Unmarshal(eventsData.Data, &data); err != nil {
			s.logger.Errorf("handleEvents: method: %s, failed to decode Data: %s", eventsData.Method, err.Error())
			return err
		}

		params := mqtt.TopicEventsRequest[media_request.FileUploadCallback]{
			Tid:       eventsData.Tid,
			Bid:       eventsData.Bid,
			Timestamp: eventsData.Timestamp,
			Data:      data,
			Method:    eventsData.Method,
			Gateway:   eventsData.Gateway,
			NeedReply: eventsData.NeedReply,
		}

		if err = NewMediaService().FileUploadCallbackHandle(params); err != nil {
			s.logger.Warnf("file_upload_callback: payload: %s", payload)
			return err
		}
	case fileds.Drc_Status_Notify:
		var data control_request.DrcStatusNotify
		if err = json.Unmarshal(eventsData.Data, &data); err != nil {
			s.logger.Errorf("handleEvents: method: %s, failed to decode Data: %s", eventsData.Method, err.Error())
			return err
		}

		params := mqtt.TopicEventsRequest[control_request.DrcStatusNotify]{
			Tid:       eventsData.Tid,
			Bid:       eventsData.Bid,
			Timestamp: eventsData.Timestamp,
			Data:      data,
			Method:    eventsData.Method,
			Gateway:   eventsData.Gateway,
			NeedReply: eventsData.NeedReply,
		}

		if err = NewDockDRCService().drcStatusNotifyHandle(params); err != nil {
			return err
		}
	case fileds.HMS:
		var data hms_request.HMSList
		if err = json.Unmarshal(eventsData.Data, &data); err != nil {
			s.logger.Errorf("handleEvents: method: %s, failed to decode Data: %s", eventsData.Method, err.Error())
			return err
		}

		params := mqtt.TopicEventsRequest[hms_request.HMSList]{
			Tid:       eventsData.Tid,
			Bid:       eventsData.Bid,
			Timestamp: eventsData.Timestamp,
			Data:      data,
			Method:    eventsData.Method,
			Gateway:   eventsData.Gateway,
			NeedReply: eventsData.NeedReply,
		}

		//if len(data.List) > 0 {
		//	s.logger.Infof("hms: gatewaySn: %s, payload: %s", eventsData.Gateway, string(payload))
		//}

		if err = NewHmsService().HmsHandle(params); err != nil {
			return err
		}

		break
	case fileds.Speaker_Audio_Play_Start_Progress:
		//s.logger.Infof("speaker_audio_play_start_progress: payload: %s", string(payload))

		var data psdk_request.SpeakerAudioPlayStartProgress
		if err = json.Unmarshal(eventsData.Data, &data); err != nil {
			s.logger.Errorf("handleEvents: method: %s, failed to decode Data: %s", eventsData.Method, err.Error())
			return err
		}

		params := mqtt.TopicEventsRequest[psdk_request.SpeakerAudioPlayStartProgress]{
			Tid:       eventsData.Tid,
			Bid:       eventsData.Bid,
			Timestamp: eventsData.Timestamp,
			Data:      data,
			Method:    eventsData.Method,
			Gateway:   eventsData.Gateway,
			NeedReply: eventsData.NeedReply,
		}

		if err = NewDockPsdkService().HandleSpeakerAudioPlayStartProgress(params); err != nil {
			return err
		}
	case fileds.Speaker_tts_play_start_progress:
		//s.logger.Infof("speaker_tts_play_start_progress: payload: %s", string(payload))

		var data psdk_request.SpeakerTtsPlayStartProgress
		if err = json.Unmarshal(eventsData.Data, &data); err != nil {
			s.logger.Errorf("handleEvents: method: %s, failed to decode Data: %s", eventsData.Method, err.Error())
			return err
		}

		params := mqtt.TopicEventsRequest[psdk_request.SpeakerTtsPlayStartProgress]{
			Tid:       eventsData.Tid,
			Bid:       eventsData.Bid,
			Timestamp: eventsData.Timestamp,
			Data:      data,
			Method:    eventsData.Method,
			Gateway:   eventsData.Gateway,
			NeedReply: eventsData.NeedReply,
		}

		if err = NewDockPsdkService().HandleSpeakerTtsPlayStartProgress(params); err != nil {
			return err
		}
	case fileds.InFlightWaylineProgress:
		var data wayline_request.InFlightWaylineProgress
		if err = json.Unmarshal(eventsData.Data, &data); err != nil {
			s.logger.Errorf("handleEvents: method: %s, failed to decode Data: %s", eventsData.Method, err.Error())
			return err
		}

		params := mqtt.TopicEventsRequest[wayline_request.InFlightWaylineProgress]{
			Tid:       eventsData.Tid,
			Bid:       eventsData.Bid,
			Timestamp: eventsData.Timestamp,
			Data:      data,
			Method:    eventsData.Method,
			Gateway:   eventsData.Gateway,
			NeedReply: eventsData.NeedReply,
		}

		if err = NewFightTaskService().inFlightWaylineProgressHandle(params); err != nil {
			s.logger.Errorf("inFlightWaylineProgressHandle: failed to handle in flight wayline progress, gateway: %s, bid: %s, err: %v", eventsData.Gateway, eventsData.Bid, err)
			return err
		}
	}

	return nil
}

func (s *RouterService) handelServicesReply(payload []byte) error {
	var mqttReply mqtt.CommonTopicResponse[mqtt.MqttReply]
	if err := json.Unmarshal(payload, &mqttReply); err != nil {
		s.logger.Errorf("handelServicesReply: failed to decode payload: %s", err.Error())
		return err
	}

	if funk.ContainsString(fileds.RemoteDebugRespList, mqttReply.Method) {
		var respData mqtt.CommonTopicResponse[mqtt.MqttReplyWithOutput[debug_response.RemoteDebugResponse]]
		if err := json.Unmarshal(payload, &respData); err != nil {
			s.logger.Errorf("handelServicesReply: failed to decode remote debug payload: %s", err.Error())
			return err
		}

		instance, err := cloudinfra.GetInstance[mqtt.CommonTopicResponse[mqtt.MqttReplyWithOutput[debug_response.RemoteDebugResponse]]](mqttReply.Tid, false)
		if err != nil {
			return err
		}

		if err = instance.Put(mqttReply.Tid, &respData); err != nil {
			return err
		}
	} else {
		instance, err := cloudinfra.GetInstance[mqtt.CommonTopicResponse[mqtt.MqttReply]](mqttReply.Tid, false)
		if err != nil {
			return err
		}

		if err = instance.Put(mqttReply.Tid, &mqttReply); err != nil {
			return err
		}
	}

	return nil
}

func (s *RouterService) handelDrcUp(deviceSn string, payload []byte) error {
	var drcData mqtt.TopicDrcRequest[json.RawMessage]
	err := json.Unmarshal(payload, &drcData)
	if err != nil {
		s.logger.Errorf("handelDrcUp: failed to decode payload: %s", err.Error())
		return err
	}

	switch drcData.Method {
	case fileds.Drc_Camera_Osd_Info_Push:
		var data control_request.DrcCameraOsd
		if err = json.Unmarshal(drcData.Data, &data); err != nil {
			s.logger.Errorf("handelDrcUp: method: %s, failed to decode Data: %s", drcData.Method, err.Error())
			return err
		}

		params := mqtt.TopicDrcRequest[control_request.DrcCameraOsd]{
			Data:      data,
			Timestamp: drcData.Timestamp,
			Method:    drcData.Method,
		}

		if err = NewDockDRCService().DrcCameraOsdInfoPushHandle(deviceSn, params); err != nil {
			return err
		}

	case fileds.Drc_Psdk_State_Info:
		var params mqtt.TopicDrcPsdkStateInfoRequest[remote_control_request.DrcPsdkStateInfo]
		err = json.Unmarshal(payload, &params)
		if err != nil {
			s.logger.Errorf("handleEvents: failed to decode payload: %s", err.Error())
			return err
		}

		if err = NewDockRemoteControlService().HandleDrcPsdkStateInfo(deviceSn, params); err != nil {
			return err
		}

	case fileds.Drc_Speaker_Play_Progress:
		//s.logger.Infof("drc_speaker_play_progress: payload: %s", string(payload))
		//{"data":{},"method":"drc_speaker_play_progress","seq":14099,"timestamp":1744797375482}

	case fileds.Osd_Info_Push:
		// fixme 暂不需要记录姿态数据，需要再开启
		// {
		//   "data": {
		//     "attitude_head": -124.80000000000001,
		//     "gimbal_pitch": -90,
		//     "gimbal_roll": 0,
		//     "gimbal_yaw": -124.61237018204584,
		//     "height": 104.98812255859374,
		//     "latitude": 26.058358292021204,
		//     "longitude": 119.25297883967053,
		//     "speed_x": 0,
		//     "speed_y": 0,
		//     "speed_z": 3.4000000000000004
		//   },
		//   "method": "osd_info_push",
		//   "timestamp": 1741936664002
		// }
		var data control_request.OsdDrc
		if err = json.Unmarshal(drcData.Data, &data); err != nil {
			s.logger.Errorf("handelDrcUp: method: %s, failed to decode Data: %s", drcData.Method, err.Error())
			return err
		}

		params := mqtt.TopicDrcRequest[control_request.OsdDrc]{
			Data:      data,
			Timestamp: drcData.Timestamp,
			Method:    drcData.Method,
		}

		if err = NewDockDRCService().osdInfoPushHandle(deviceSn, params); err != nil {
			return err
		}
	case fileds.DRONE_CONTROL:
		// {
		//   "data": {
		//     "output": {"seq": 407},
		//     "result": 319045
		//   },
		//   "method": "drone_control"
		// }
		var params mqtt.TopicDrcDroneControlRequest
		err = json.Unmarshal(payload, &params)
		if err != nil {
			s.logger.Errorf("handelDrcUp: failed to decode payload: %s", err.Error())
			return err
		}

		if err = NewDockDRCService().droneControlHandle(deviceSn, params); err != nil {
			return err
		}
	case fileds.HSI_INFO_PUSH:
		// {
		//   "data": {
		//     "around_distances": [],
		//     "back_enable": true,
		//     "back_work": true,
		//     "down_distance": 60000,
		//     "down_enable": true,
		//     "down_work": true,
		//     "front_enable": true,
		//     "front_work": true,
		//     "horizontal_enable": true,
		//     "horizontal_work": true,
		//     "left_enable": true,
		//     "left_work": true,
		//     "right_enable": true,
		//     "right_work": true,
		//     "up_distance": 60000,
		//     "up_enable": true,
		//     "up_work": true,
		//     "vertical_enable": true,
		//     "vertical_work": true
		//   },
		//   "method": "hsi_info_push",
		//   "timestamp": 1741936663004
		// }
		break
	case fileds.DELAY_INFO_PUSH:
		// {
		//   "data": {
		//     "sdr_cmd_delay": 27
		//   },
		//   "method": "delay_info_push",
		//   "timestamp": 1741936663002
		// }
		break
	case fileds.DRC_LIGHT_CALIBRATION:
		//s.logger.Infof("drc_light_calibration: payload: %s", string(payload))

	case fileds.DRC_LIGHT_MODE_SET:
		//s.logger.Infof("drc_light_mode_set: payload: %s", string(payload))

	case fileds.DRC_LIGHT_FINE_TUNING_SET:
		//s.logger.Infof("drc_light_fine_tuning_set: payload: %s", string(payload))

	case fileds.DRC_LIGHT_BRIGHTNESS_SET:
		//s.logger.Infof("drc_light_brightness_set: payload: %s", string(payload))

	case fileds.DRC_SPEAKER_TTS_SET:
		s.logger.Infof("drc_speaker_tts_set: payload: %s", string(payload))

	}

	return nil
}
