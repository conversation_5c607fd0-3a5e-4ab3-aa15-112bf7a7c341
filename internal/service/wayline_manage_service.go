package service

import (
	"encoding/json"
	"fmt"
	"math"
	"sync"
	"wukong-api/internal/dto"
	"wukong-api/internal/model"
	"wukong-api/internal/repo"

	"github.com/qxsugar/pkg/apix"
	"github.com/qxsugar/pkg/dbx"
	"github.com/thoas/go-funk"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	waylineManageServiceOnce sync.Once
	waylineManageService     *WaylineManageService
)

type WaylineManageService struct {
	db                 *gorm.DB
	logger             *zap.SugaredLogger
	waylineFileService *WaylineFileService
}

func NewWaylineManageService() *WaylineManageService {
	waylineManageServiceOnce.Do(func() {
		waylineManageService = &WaylineManageService{
			db:                 repo.GetDatabase(),
			logger:             repo.GetLogger(),
			waylineFileService: NewWaylineFileService(),
		}
	})
	return waylineManageService
}

func (s *WaylineManageService) Upload(params *dto.WaylineManageCreateReq, tmpKMZPath string, cid int, userId int) error {
	tx := s.db.Begin()
	routeFileFunc := UploadTypeMapping[RouteFile]
	objectKey, _ := routeFileFunc(nil, cid, fmt.Sprintf("%s.kmz", params.FileName), ExtInfo{})
	//上传到oss中
	_, _, err := NewOssService().PutObjectFromFile(tmpKMZPath, objectKey)
	if err != nil {
		tx.Rollback()
		return err
	}
	waylineFile, err := s.waylineFileService.CreateWaylineFile(params.Name, params.FileName, objectKey, cid, userId, tx)
	if err != nil {
		tx.Rollback()
		return err
	}
	url, err := NewOssService().GetObjectSignURL(objectKey, 60*60)
	if err != nil {
		tx.Rollback()
		return err
	}
	kml, err := s.waylineFileService.ParseKML(url)
	if err != nil {
		tx.Rollback()
		return err
	}
	waylineManage := &model.WaylineManage{
		Name:                   params.Name,
		WaylineFileID:          waylineFile.ID,
		CompanyID:              cid,
		WaylineType:            1,
		ExpectedFlightMileage:  kml.ExpectedFlightMileage,
		ExpectedFlightDuration: math.Round(((kml.ExpectedFlightMileage/kml.KMLFlightParams.AutoFlightSpeed)/60)*10) / 10,
		PointsData:             "",
		IsImport:               true,
	}

	err = tx.Create(waylineManage).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()

	return nil
}

func (s *WaylineManageService) Create(params *dto.WaylineManageCreateReq, cid int, userId int) error {
	tx := s.db.Begin()

	var waylineFile *model.WaylineFile
	var err error

	// fixme 演示逻辑
	isMockFuzhou := false
	//if cid == 91 {
	//	if strings.Contains(params.Name, "地保") {
	//		isMockFuzhou = true
	//
	//		// 直接绑定该航线
	//		//waylineFile, err = s.waylineFileService.GetWaylineById(292)
	//		waylineFile, err = s.waylineFileService.GetWaylineById(306)
	//		if err != nil {
	//			tx.Rollback()
	//			return err
	//		}
	//	}
	//	if strings.Contains(params.Name, "特别保护区") {
	//		isMockFuzhou = true
	//
	//		waylineFile, err = s.waylineFileService.GetWaylineById(294)
	//		if err != nil {
	//			tx.Rollback()
	//			return err
	//		}
	//	}
	//}

	if !isMockFuzhou {
		// 生成KMZ
		KMZPath, err := s.waylineFileService.CreateKmz(params.PointsData)
		if err != nil {
			tx.Rollback()
			return err
		}
		routeFileFunc := UploadTypeMapping[RouteFile]
		objectKey, _ := routeFileFunc(nil, cid, fmt.Sprintf("%s.kmz", params.FileName), ExtInfo{})

		//上传到oss中
		_, _, err = NewOssService().PutObjectFromFile(KMZPath, objectKey)
		if err != nil {
			tx.Rollback()
			return err
		}

		waylineFile, err = s.waylineFileService.CreateWaylineFile(params.Name, params.FileName, objectKey, cid, userId, tx)
		if err != nil {
			tx.Rollback()
			return err
		}
	}

	waylineManage := &model.WaylineManage{
		Name:                   params.Name,
		WaylineFileID:          waylineFile.ID,
		CompanyID:              cid,
		WaylineType:            params.WaylineType,
		ExpectedFlightMileage:  params.ExpectedFlightMileage,
		ExpectedFlightDuration: params.ExpectedFlightDuration,
		PointsData:             params.PointsData,
	}

	err = tx.Create(waylineManage).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()

	return nil
}

func (s *WaylineManageService) List(params *dto.WaylineManageListReq, cid int) (interface{}, error) {
	count := int64(0)
	list := make([]model.WaylineManageWithWaylineFile, 0, params.Limit)

	op := s.db.Model(model.WaylineManage{}).
		Select("wayline_manage.*, wayline_file.file_name, wayline_file.file_url, wayline_file.object_key").
		Joins("left join wayline_file on wayline_manage.wayline_file_id = wayline_file.id").
		//Where("wayline_manage.id NOT IN ?", []uint{220, 216, 215, 214}) // 排除桥梁绕飞航线
		Where("wayline_manage.id NOT IN ?", []uint{220, 216, 215, 248, 249, 264, 262}) // 排除桥梁绕飞航线

	if params.WaylineType != 0 {
		op.Where("wayline_manage.wayline_type = ?", params.WaylineType)
	}
	if params.Name != "" {
		op = op.Where("wayline_manage.name like ?", dbx.LikeString(params.Name))
	}

	op.Where("wayline_manage.company_id = ?", cid).Order("wayline_manage.id desc").Count(&count).Offset(params.Offset).Limit(params.Limit).Find(&list)
	err := op.Error
	if err != nil {
		return nil, err
	}

	return apix.PageBody{
		Limit:  params.Limit,
		Offset: params.Offset,
		Total:  count,
		List:   list,
	}, nil
}

func (s *WaylineManageService) Detail(id int) (interface{}, error) {
	waylineInfo, err := s.GetWaylineManageWithFileById(id)
	if err != nil {
		return nil, err
	}

	var url string
	url, err = NewOssService().GetObjectSignURL(waylineInfo.ObjectKey, 60*60)
	if err != nil {
		return nil, err
	}

	waylineInfo.FileUrl = url

	parseData, err := s.waylineFileService.ParseKML(url)
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"wayline_info": waylineInfo,
		"wayline":      parseData.Points,
	}, nil
}

func (s *WaylineManageService) Update(params *dto.WaylineManageUpdateReq, cid int) error {
	var waylineManage model.WaylineManage
	err := s.db.Model(&model.WaylineManage{}).Where("id = ?", params.ID).First(&waylineManage).Error
	if err != nil {
		return err
	}

	tx := s.db.Begin()
	// 生成KMZ
	KMZPath, err := s.waylineFileService.CreateKmz(params.PointsData)
	if err != nil {
		tx.Rollback()
		return err
	}
	routeFileFunc := UploadTypeMapping[RouteFile]
	objectKey, _ := routeFileFunc(nil, cid, fmt.Sprintf("%s.kmz", params.FileName), ExtInfo{})

	err = s.waylineFileService.UpdateWaylineFile(waylineManage.WaylineFileID, params.Name, params.FileName, objectKey, KMZPath, tx)
	if err != nil {
		tx.Rollback()
		return err
	}

	update := &model.WaylineManage{
		Name:                   params.Name,
		WaylineType:            params.WaylineType,
		ExpectedFlightMileage:  params.ExpectedFlightMileage,
		ExpectedFlightDuration: params.ExpectedFlightDuration,
		PointsData:             params.PointsData,
	}

	err = tx.Model(&model.WaylineManage{}).Where("id = ?", params.ID).Updates(update).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()

	return nil
}

func (s *WaylineManageService) Delete(id int, cid int) error {
	count := int64(0)
	err := s.db.Model(&model.InspectionJobRecord{}).Where("wayline_manage_id = ? AND status = ?", id, InspectionJobStatusWorking).Count(&count).Error
	if err != nil {
		return err
	}

	if count > 0 {
		err = fmt.Errorf("当前航线正在执行中，无法删除")
		return err
	}

	var waylineManage model.WaylineManage
	err = s.db.Model(&model.WaylineManage{}).Where("id = ?", id).First(&waylineManage).Error
	if err != nil {
		return err
	}

	tx := s.db.Begin()

	err = tx.Where("id = ?", id).Delete(&model.WaylineManage{}).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	err = s.waylineFileService.DelWaylineFileById(waylineManage.WaylineFileID, tx)
	if err != nil {
		tx.Rollback()
		return err
	}

	// 删除关联
	jobList := make([]model.InspectionJob, 0)
	err = tx.Model(model.InspectionJob{}).Where("company_id = ?", cid).Find(&jobList).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	for _, job := range jobList {
		var waylineManageIDList []int
		if err = json.Unmarshal([]byte(job.WaylineManageIDList), &waylineManageIDList); err != nil {
			tx.Rollback()
			return err
		}

		// 去掉一样的id
		newWaylineManageIDList := funk.Filter(waylineManageIDList, func(waylineManageID int) bool {
			return waylineManageID != id
		}).([]int)

		// 如果有变化就更新
		if len(waylineManageIDList) != len(newWaylineManageIDList) {
			var encodedList []byte
			encodedList, err = json.Marshal(newWaylineManageIDList)
			if err != nil {
				tx.Rollback()
				return err
			}

			err = tx.Model(model.InspectionJob{}).Where("id = ?", job.ID).Update("wayline_manage_id_list", string(encodedList)).Error
			if err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	err = tx.Model(&model.InspectionJobRecord{}).Where("wayline_manage_id = ?", id).Update("wayline_manage_id", 0).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	err = tx.Model(&model.JobLogs{}).Where("wayline_manage_id = ?", id).Update("wayline_manage_id", 0).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()

	return nil
}

func (s *WaylineManageService) GetWaylineManageWithFileById(id int) (*model.WaylineManageWithWaylineFile, error) {
	var waylineManageWithWaylineFile model.WaylineManageWithWaylineFile
	err := s.db.Model(&model.WaylineManage{}).
		Select("wayline_manage.*, wayline_file.file_name, wayline_file.file_url, wayline_file.object_key").
		Joins("left join wayline_file on wayline_manage.wayline_file_id = wayline_file.id").
		Where("wayline_manage.id = ?", id).
		First(&waylineManageWithWaylineFile).
		Error

	if err != nil {
		return nil, err
	}

	return &waylineManageWithWaylineFile, nil
}

func (s *WaylineManageService) GetWaylineManageById(id int) (*model.WaylineManage, error) {
	var waylineManage model.WaylineManage
	err := s.db.Model(&model.WaylineManage{}).Where("id = ?", id).First(&waylineManage).Error
	if err != nil {
		return nil, err
	}

	return &waylineManage, nil
}

func (s *WaylineManageService) GetParsedDataById(id int) (*dto.WaylineParsedDataDto, error) {
	wayline, err := s.GetWaylineManageWithFileById(id)
	if err != nil {
		return nil, err
	}

	var url string
	url, err = NewOssService().GetObjectSignURL(wayline.ObjectKey, 60*60)
	if err != nil {
		return nil, err
	}

	// todo 缓存解析结果

	waylineParsedData, err := s.waylineFileService.ParseKML(url)
	if err != nil {
		return nil, err
	}

	targetTrajectoryData, err := json.Marshal(waylineParsedData.Points)
	if err != nil {
		return nil, err
	}

	return &dto.WaylineParsedDataDto{
		WaylineManageId:      id,
		WaylineFileId:        wayline.WaylineFileID,
		WaylineName:          wayline.Name,
		TargetTrajectoryData: string(targetTrajectoryData),
		ReturnKMLData:        *waylineParsedData,
	}, nil
}
