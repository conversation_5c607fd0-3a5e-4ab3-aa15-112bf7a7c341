package service

import (
	"encoding/json"
	"fmt"
	"math"
	"wukong-api/internal/dto"
	"wukong-api/internal/enum"
	"wukong-api/pkg/utils"

	"github.com/qxsugar/pkg/dbx"

	"sync"
	"time"
	"wukong-api/internal/model"
	"wukong-api/internal/repo"

	"github.com/go-redis/redis"
	"github.com/qxsugar/pkg/apix"

	"github.com/thoas/go-funk"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	InspectionJobRecordServiceOnce sync.Once
	inspectionJobRecordService     *InspectionJobRecordService
)

type InspectionJobRecordService struct {
	db         *gorm.DB
	logger     *zap.SugaredLogger
	redis      *redis.Client
	ossService *OssService
}

func NewInspectionJobRecordService() *InspectionJobRecordService {
	InspectionJobRecordServiceOnce.Do(func() {
		inspectionJobRecordService = &InspectionJobRecordService{
			db:         repo.GetDatabase(),
			logger:     repo.GetLogger(),
			redis:      repo.GetRedis(0),
			ossService: NewOssService(),
		}
	})
	return inspectionJobRecordService
}

func (s *InspectionJobRecordService) ImageDown(id int) ([]byte, error) {
	var jobRecord model.InspectionJobRecord
	err := s.db.Model(model.InspectionJobRecord{}).Where("id = ?", id).First(&jobRecord).Error
	if err != nil {
		return nil, err
	}

	jobMedia := make([]model.MediaFile, 0, 0)
	opMedia := s.db.Model(model.MediaFile{})

	if jobRecord.Type == 1 {
		opMedia.Where("manual_job_id = ?", jobRecord.ManualJobID)
	} else if jobRecord.Type == 2 {
		opMedia.Where("wayline_job_id = ?", jobRecord.WaylineJobID)
	}

	err = opMedia.Find(&jobMedia).Error
	if err != nil {
		return nil, err
	}

	ossService := NewOssService()
	expire := 60 * 60 * 24

	// 获取资源文件的url
	jobMediaUrlSyncMap := sync.Map{}
	var wg sync.WaitGroup

	jobMediaObjectKeyDict := make(map[int]string)
	for _, media := range jobMedia {
		jobMediaObjectKeyDict[media.ID] = media.ObjectKey
	}

	for jobMediaId, objectKey := range jobMediaObjectKeyDict {
		wg.Add(1)
		go func(id int, key string) {
			defer wg.Done()

			var url string
			if url, err = ossService.GetObjectSignURL(key, int64(expire)); err != nil {
				s.logger.Errorf("failed to get sign url, jobMediaId: %d, key: %s, err: %v", id, key, err)
				return
			}

			jobMediaUrlSyncMap.Store(id, url)
		}(jobMediaId, objectKey)
	}

	wg.Wait()

	jobMediaUrlMap := make(map[int]string)
	jobMediaUrlSyncMap.Range(func(key, value interface{}) bool {
		jobMediaUrlMap[key.(int)] = value.(string)
		return true
	})

	// 组装resp
	urls := make([]string, 0)
	// 作业照片
	for _, media := range jobMedia {
		if media.FileType == 1 {
			urls = append(urls, jobMediaUrlMap[media.ID])
		}
	}
	return utils.DownloadImagesToZip(urls)
}

func (s *InspectionJobRecordService) Create(inspectionJob model.InspectionJob, creatorID int, manualJobID string, waylineJobId string, waylineData dto.WaylineParsedDataDto, isTmp bool) (*model.InspectionJobRecord, error) {
	bytes, err := json.Marshal(waylineData.ReturnKMLData.KMLFlightParams)
	if err != nil {
		return nil, err
	}

	inspectionJobRecord := model.InspectionJobRecord{
		InspectionJobID:       inspectionJob.ID,
		AIModelList:           inspectionJob.AIModelList,
		WaylineManageID:       waylineData.WaylineManageId,
		CreatorID:             creatorID,
		ObjectName:            inspectionJob.ObjectName,
		WaylineOverview:       waylineData.WaylineName,
		WaylineName:           waylineData.WaylineName,
		Type:                  inspectionJob.Type,
		Name:                  inspectionJob.Name,
		ExecuteType:           inspectionJob.ExecuteType,
		CompanyID:             inspectionJob.CompanyID,
		TargetTrajectory:      waylineData.TargetTrajectoryData,
		ExpectedFlightMileage: waylineData.ReturnKMLData.ExpectedFlightMileage,
		ManualJobID:           manualJobID,
		WaylineJobID:          waylineJobId,
		Person:                inspectionJob.Person,
		PersonNumber:          inspectionJob.PersonNumber,
		KMLFlightParams:       string(bytes),
		AlgorithmTarget:       inspectionJob.AlgorithmTarget,
		IsTmp:                 isTmp,
	}

	if inspectionJob.Scene != 0 {
		inspectionJobRecord.Scene = inspectionJob.Scene
	} else {
		// fixme 兼容老数据
		if inspectionJob.CompanyID == 93 {
			if waylineData.WaylineManageId == 139 {
				inspectionJobRecord.Scene = enum.JobSceneTypeEnum_Bridge.Value()
			} else {
				inspectionJobRecord.Scene = enum.JobSceneTypeEnum_Road.Value()
			}
		} else if inspectionJob.CompanyID == 94 {
			if waylineData.WaylineManageId == 209 || waylineData.WaylineManageId == 214 {
				inspectionJobRecord.Scene = enum.JobSceneTypeEnum_Bridge.Value()
			} else {
				inspectionJobRecord.Scene = enum.JobSceneTypeEnum_SubwayGround.Value()
			}
		} else {
			inspectionJobRecord.Scene = enum.JobSceneTypeEnum_SubwayGround.Value()
		}
	}

	err = s.db.Create(&inspectionJobRecord).Error
	if err != nil {
		return nil, err
	}

	return &inspectionJobRecord, nil
}

func (s *InspectionJobRecordService) List(params *dto.InspectionJobRecordListReq, cid int) (interface{}, error) {
	var wg sync.WaitGroup
	var errChan = make(chan error, 4)

	// 并行查询所有需要的数据
	var count int64
	var recordList []model.InspectionJobRecordAfterJoinJob
	var dronePilotList []model.DronePilot

	wg.Add(2)

	// 查询主记录和总数
	go func() {
		defer wg.Done()
		op := s.db.Model(&model.InspectionJobRecord{}).
			Select("inspection_job_record.*, manual_job.vehicle_id as vehicle_id, manual_job.drone_pilot_id as drone_pilot_id, wayline_job.dock_sn as dock_sn, wayline_job.drone_sn as drone_sn, manual_job.vehicle_name as vehicle_name, wayline_job.dock_name as dock_name, wayline_job.drone_name as drone_name").
			Joins("left join manual_job on manual_job.job_id = inspection_job_record.manual_job_id").
			Joins("left join wayline_job on wayline_job.job_id = inspection_job_record.wayline_job_id")

		if params.StartTime != 0 {
			op.Where("inspection_job_record.created_at >= ?", time.Unix(params.StartTime, 0))
		}
		if params.EndTime != 0 {
			op.Where("inspection_job_record.created_at <= ?", time.Unix(params.EndTime, 0))
		}
		if params.Type != 0 {
			op.Where("inspection_job_record.type = ?", params.Type)
		}
		if params.Status != 0 {
			op.Where("inspection_job_record.status = ?", params.Status)
		}
		if params.Name != "" {
			condition := dbx.LikeString(params.Name)
			op.Where("inspection_job_record.name like ? or inspection_job_record.wayline_name like ?", condition, condition)
		}
		if params.RecordID != 0 {
			op.Where("inspection_job_record.id = ?", params.RecordID)
		}
		if params.IsWarn != nil {
			// 处理是否有告警的筛选条件
			if *params.IsWarn == true {
				// 有告警记录的任务
				op.Where("EXISTS (SELECT 1 FROM job_warn_record WHERE job_warn_record.is_deleted = 0 AND job_warn_record.job_record_id = inspection_job_record.id)")
			} else {
				// 没有告警记录的任务
				op.Where("NOT EXISTS (SELECT 1 FROM job_warn_record WHERE job_warn_record.is_deleted = 0 AND job_warn_record.job_record_id = inspection_job_record.id)")
			}
		}
		// 切换行业场景 地保、桥梁、接触网
		if params.RecordSceneType == "fuzhou_subway" {
			op.Where("inspection_job_record.id not in (?)", []int{1147, 1148, 1149, 1150})
		} else if params.RecordSceneType == "fuzhou_bridge" {
			op.Where("inspection_job_record.id = ?", 1147).Or("inspection_job_record.id = ?", 1149)
		} else if params.RecordSceneType == "fuzhou_net" {
			op.Where("inspection_job_record.id = ?", 1148).Or("inspection_job_record.id = ?", 1150)
		}

		if params.RecordSceneType == "xiamen_subway" {
			op.Where("inspection_job_record.id not in (?)", []int{1972, 1971, 1969})             // 排除指定桥梁记录
			op.Where("inspection_job_record.wayline_manage_id not in (?)", []int{209, 214, 268}) // 排除接触网、桥梁航线
		} else if params.RecordSceneType == "xiamen_bridge" {
			op.Where("inspection_job_record.id IN ? or inspection_job_record.wayline_manage_id IN ?", []int{1972, 1971, 1969}, []int{214, 268}) // 指定桥梁记录 或 指定桥梁航线
		} else if params.RecordSceneType == "xiamen_net" {
			op.Where("inspection_job_record.wayline_manage_id = ?", 209) // 接触网航线
		}

		if params.RecordSceneType != "fuzhou_bridge" && params.RecordSceneType != "fuzhou_net" && params.RecordID == 0 {
			op.Where("inspection_job_record.company_id = ?", cid)
		}

		if err := op.Count(&count).Error; err != nil {
			errChan <- err
			return
		}
		if err := op.Order("inspection_job_record.id desc").Offset(params.Offset).Limit(params.Limit).Scan(&recordList).Error; err != nil {
			errChan <- err
			return
		}
	}()

	// 查询飞手列表
	go func() {
		defer wg.Done()
		if err := s.db.Model(model.DronePilot{}).
			Select("id, name").
			// Where("company_id = ?", cid).
			Find(&dronePilotList).Error; err != nil {
			errChan <- err
		}
	}()

	wg.Wait()
	close(errChan)

	// 检查是否有错误
	for err := range errChan {
		if err != nil {
			return nil, err
		}
	}

	pilotMap := make(map[int]string)
	for _, pilot := range dronePilotList {
		pilotMap[pilot.ID] = pilot.Name
	}

	// 收集所有记录ID，用于批量查询告警记录
	recordIds := make([]int, len(recordList))
	for i, record := range recordList {
		recordIds[i] = record.ID
	}

	// 批量查询告警记录
	var warnRecords []model.JobWarnRecord
	if err := s.db.Model(model.JobWarnRecord{}).
		Select("id, job_record_id").
		Where("job_record_id IN ?", recordIds).
		Find(&warnRecords).Error; err != nil {
		return nil, err
	}

	// 创建告警记录映射
	warnRecordMap := make(map[int][]int)
	for _, warn := range warnRecords {
		warnRecordMap[warn.JobRecordID] = append(warnRecordMap[warn.JobRecordID], warn.ID)
	}

	// 并发获取OSS URL
	ossSvc := NewOssService()
	urlSyncMap := sync.Map{}
	var urlWg sync.WaitGroup

	for _, record := range recordList {
		if record.ReportDocxObjectKey == "" && record.TaskTrajectoryPic == "" {
			continue
		}
		urlWg.Add(1)
		go func(id int, docxKey, trajectoryKey string) {
			defer urlWg.Done()
			if docxKey != "" {
				if url, err := ossSvc.GetObjectSignURL(docxKey, int64(60*60*24)); err == nil {
					urlSyncMap.Store(fmt.Sprintf("%d_docx", id), url)
				}
			}
			if trajectoryKey != "" {
				if url, err := ossSvc.GetObjectSignURL(trajectoryKey, int64(60*60*24)); err == nil {
					urlSyncMap.Store(fmt.Sprintf("%d_trajectory", id), url)
				}
			}
		}(record.ID, record.ReportDocxObjectKey, record.TaskTrajectoryPic)
	}

	urlWg.Wait()

	// 构建响应列表
	respList := make([]dto.InspectionJobRecordListResp, len(recordList))
	for i, record := range recordList {
		var docxURL, trajectoryPicURL string
		if v, ok := urlSyncMap.Load(fmt.Sprintf("%d_docx", record.ID)); ok {
			docxURL = v.(string)
		}
		if v, ok := urlSyncMap.Load(fmt.Sprintf("%d_trajectory", record.ID)); ok {
			trajectoryPicURL = v.(string)
		}

		var executeDrone, pilotName string
		if record.Type == 1 {
			executeDrone = record.VehicleName
			pilotName = pilotMap[record.DronePilotId]
		} else if record.Type == 2 {
			executeDrone = record.DroneName
		}

		respList[i] = dto.InspectionJobRecordListResp{
			ID:                   record.ID,
			InspectionJobID:      record.InspectionJobID,
			ManualJobID:          record.ManualJobID,
			WaylineJobID:         record.WaylineJobID,
			AIModelList:          record.AIModelList,
			WaylineManageID:      record.WaylineManageID,
			ObjectName:           record.ObjectName,
			WaylineOverview:      record.WaylineOverview,
			WaylineName:          record.WaylineName,
			AreaInfo:             record.AreaInfo,
			ExecuteTime:          record.ExecuteTime,
			CompletedTime:        record.CompletedTime,
			Percent:              record.Percent,
			Type:                 record.Type,
			FlightMileage:        record.FlightMileage,
			FlightDuration:       record.FlightDuration,
			Name:                 record.Name,
			ExecuteType:          record.ExecuteType,
			CompanyID:            record.CompanyID,
			TaskTrajectory:       record.TaskTrajectory,
			TargetTrajectory:     record.TargetTrajectory,
			Status:               record.Status,
			ExecuteDrone:         executeDrone,
			PilotName:            pilotName,
			DockName:             record.DockName,
			WarnRecordIdList:     warnRecordMap[record.ID],
			Person:               record.Person,
			PersonNumber:         record.PersonNumber,
			ReportDocxURL:        docxURL,
			AlgorithmTarget:      record.AlgorithmTarget,
			TaskTrajectoryPicUrl: trajectoryPicURL,
			LiveStartTime:        record.LiveStartTime,
			IsTmp:                record.IsTmp,
		}
	}

	return apix.PageBody{
		Limit:  params.Limit,
		Offset: params.Offset,
		Total:  count,
		List:   respList,
	}, nil
}

func (s *InspectionJobRecordService) Detail(id int) (interface{}, error) {
	var jobRecord model.InspectionJobRecord
	err := s.db.Model(model.InspectionJobRecord{}).Where("id = ?", id).First(&jobRecord).Error
	if err != nil {
		return nil, err
	}

	jobLogs := make([]model.JobLogs, 0, 0)
	err = s.db.Model(model.JobLogs{}).Where("job_record_id = ?", id).Find(&jobLogs).Error
	if err != nil {
		return nil, err
	}

	jobMedia := make([]model.MediaFile, 0, 0)
	opMedia := s.db.Model(model.MediaFile{}).Where("is_warn = ?", 0)
	opMedia = opMedia.Where("file_type in (?)", []int{1, 2})

	if jobRecord.Type == 1 {
		opMedia = opMedia.Where("manual_job_id = ?", jobRecord.ManualJobID)
	} else if jobRecord.Type == 2 {
		opMedia = opMedia.Where("wayline_job_id = ?", jobRecord.WaylineJobID)
	}

	err = opMedia.Find(&jobMedia).Error
	if err != nil {
		return nil, err
	}

	warnRecords := make([]model.JobWarnRecord, 0, 0)
	err = s.db.Model(model.JobWarnRecord{}).Where("job_record_id = ?", id).Find(&warnRecords).Error
	if err != nil {
		return nil, err
	}

	warnVideos := make([]model.WarnVideo, 0, 0)
	err = s.db.Model(model.WarnVideo{}).Where("job_record_id = ?", id).Find(&warnVideos).Error
	if err != nil {
		return nil, err
	}

	ossService := NewOssService()
	expire := 60 * 60 * 24

	// 获取资源文件的url
	jobMediaUrlSyncMap := sync.Map{}
	warnRecordsUrlSyncMap := sync.Map{}
	jobLogsSyncMap := sync.Map{}
	warnVideosSyncMap := sync.Map{}
	var wg sync.WaitGroup

	jobMediaObjectKeyDict := make(map[int]string)
	for _, media := range jobMedia {
		jobMediaObjectKeyDict[media.ID] = media.ObjectKey
	}

	warnRecordsObjectKeyDict := make(map[int][]string)
	for _, record := range warnRecords {
		var picList []string
		err = json.Unmarshal([]byte(record.PicList), &picList)
		if err != nil {
			return nil, err
		}

		warnRecordsObjectKeyDict[record.ID] = picList
	}

	jobLogsObjectKeyDict := make(map[int][]string)
	for _, log := range jobLogs {
		if log.PicList != "" {
			var picList []string
			err = json.Unmarshal([]byte(log.PicList), &picList)
			if err != nil {
				return nil, err
			}

			jobLogsObjectKeyDict[log.ID] = picList
		}
	}

	jobWarnVideosObjectKeyDict := make(map[int]string)
	for _, warnVideo := range warnVideos {
		jobWarnVideosObjectKeyDict[warnVideo.ID] = warnVideo.ObjectKey
	}

	for jobMediaId, objectKey := range jobMediaObjectKeyDict {
		wg.Add(1)
		go func(id int, key string) {
			defer wg.Done()

			var url string
			if url, err = ossService.GetObjectSignURL(key, int64(expire)); err != nil {
				s.logger.Errorf("failed to get sign url, jobMediaId: %d, key: %s, err: %v", id, key, err)
				return
			}

			jobMediaUrlSyncMap.Store(id, url)
		}(jobMediaId, objectKey)
	}

	for warnRecordId, objectKeyList := range warnRecordsObjectKeyDict {
		wg.Add(1)
		go func(id int, keyList []string) {
			defer wg.Done()

			urlList := make([]string, 0)
			for _, key := range keyList {
				var url string
				if url, err = ossService.GetObjectSignURL(key, int64(expire)); err != nil {
					s.logger.Errorf("failed to get sign url, warnRecordId: %d, key: %s, err: %v", id, key, err)
					return
				}
				urlList = append(urlList, url)
			}

			warnRecordsUrlSyncMap.Store(id, urlList)
		}(warnRecordId, objectKeyList)
	}

	for jobLogId, objectKeyList := range jobLogsObjectKeyDict {
		wg.Add(1)
		go func(id int, keyList []string) {
			defer wg.Done()

			urlList := make([]string, 0)
			for _, key := range keyList {
				var url string
				if url, err = ossService.GetObjectSignURL(key, int64(expire)); err != nil {
					s.logger.Errorf("failed to get sign url, jobLogId: %d, key: %s, err: %v", id, key, err)
					return
				}
				urlList = append(urlList, url)
			}

			jobLogsSyncMap.Store(id, urlList)
		}(jobLogId, objectKeyList)
	}

	for warnVideoId, objectKey := range jobWarnVideosObjectKeyDict {
		wg.Add(1)
		go func(id int, key string) {
			defer wg.Done()

			var url string
			if url, err = ossService.GetObjectSignURL(key, int64(expire)); err != nil {
				s.logger.Errorf("failed to get sign url, warnVideoId: %d, key: %s, err: %v", id, key, err)
				return
			}

			warnVideosSyncMap.Store(id, url)
		}(warnVideoId, objectKey)
	}

	wg.Wait()

	jobMediaUrlMap := make(map[int]string)
	jobMediaUrlSyncMap.Range(func(key, value interface{}) bool {
		jobMediaUrlMap[key.(int)] = value.(string)
		return true
	})

	warnRecordsUrlMap := make(map[int][]string)
	warnRecordsUrlSyncMap.Range(func(key, value interface{}) bool {
		warnRecordsUrlMap[key.(int)] = value.([]string)
		return true
	})

	jobLogsUrlMap := make(map[int][]string)
	jobLogsSyncMap.Range(func(key, value interface{}) bool {
		jobLogsUrlMap[key.(int)] = value.([]string)
		return true
	})

	warnVideosUrlMap := make(map[int]string)
	warnVideosSyncMap.Range(func(key, value interface{}) bool {
		warnVideosUrlMap[key.(int)] = value.(string)
		return true
	})

	// 组装resp
	jobMediaResp := make([]dto.MediaFileResp, 0)
	jobVideosResp := make([]dto.MediaFileResp, 0)

	// 作业照片、视频
	for _, media := range jobMedia {
		resp := dto.MediaFileResp{
			ID:             media.ID,
			FileID:         media.FileID,
			FileName:       media.FileName,
			CompanyID:      media.CompanyID,
			ObjectKey:      media.ObjectKey,
			IsOriginal:     media.IsOriginal,
			Drone:          media.Drone,
			Longitude:      media.Longitude,
			Latitude:       media.Latitude,
			ManualJobId:    media.ManualJobId,
			WaylineJobID:   media.WaylineJobID,
			DeviceModelKey: media.DeviceModelKey,
			FileType:       media.FileType,
			UpdatedAt:      media.UpdatedAt,
			CreatedAt:      media.CreatedAt,
			IsDeleted:      media.IsDeleted,
			Url:            jobMediaUrlMap[media.ID],
		}

		if media.FileType == 1 {
			jobMediaResp = append(jobMediaResp, resp)
		} else if media.FileType == 2 {
			jobVideosResp = append(jobVideosResp, resp)
		}
	}

	// 告警记录
	jobWarnRecordResp := funk.Map(warnRecords, func(record model.JobWarnRecord) dto.JobWarnRecordResp {
		var position dto.JobWarnRecordPosition
		if e := json.Unmarshal([]byte(record.Position), &position); e != nil {
			s.logger.Errorf("failed to decode job warn record position, id: %d, err: %v", record.ID, e)
		}

		var warnType []string
		if e := json.Unmarshal([]byte(record.WarnType), &warnType); e != nil {
			s.logger.Errorf("failed to decode job warn record type, id: %d, err: %v", record.ID, e)
		}

		return dto.JobWarnRecordResp{
			ID:              record.ID,
			CompanyID:       record.CompanyID,
			JobRecordID:     record.JobRecordID,
			JobRecordName:   record.JobRecordName,
			WarnDesc:        record.WarnDesc,
			PicList:         warnRecordsUrlMap[record.ID],
			Position:        position,
			Level:           record.Level,
			WarnType:        warnType,
			Status:          record.Status,
			WarnAt:          record.WarnAt,
			HandleAt:        record.HandleAt,
			CreatedAt:       record.CreatedAt,
			UpdatedAt:       record.UpdatedAt,
			DroneSN:         record.DroneSN,
			AlgorithmSource: record.AlgorithmSource,
		}
	}).([]dto.JobWarnRecordResp)

	// 任务日志
	jobLogsResp := funk.Map(jobLogs, func(log model.JobLogs) dto.JobLogsResp {
		picList := make([]string, 0)
		if log.PicList != "" {
			picList = jobLogsUrlMap[log.ID]
		}

		return dto.JobLogsResp{
			ID:              log.ID,
			JobRecordID:     log.JobRecordID,
			WaylineManageID: log.WaylineManageID,
			DroneID:         log.DroneID,
			DeviceId:        log.DeviceId,
			CompanyID:       log.CompanyID,
			Desc:            log.Desc,
			PicList:         picList,
			Longitude:       log.Longitude,
			Latitude:        log.Latitude,
			DoTime:          log.DoTime,
			CreatedAt:       log.CreatedAt,
			UpdatedAt:       log.UpdatedAt,
			IsDeleted:       0,
			IsError:         log.IsError,
		}
	}).([]dto.JobLogsResp)

	// 告警视频
	jobWarnVideosResp := funk.Map(warnVideos, func(video model.WarnVideo) dto.WarnVideoResp {
		var alarmInfo []dto.AlarmDto
		if e := json.Unmarshal([]byte(video.AlarmInfo), &alarmInfo); e != nil {
			s.logger.Errorf("failed to decode job warn video, id: %d, err: %v", video.ID, e)
		}

		return dto.WarnVideoResp{
			ID:             video.ID,
			CompanyID:      video.CompanyID,
			JobRecordID:    video.JobRecordID,
			OriginalFileID: video.OriginalFileID,
			FileName:       video.FileName,
			ObjectKey:      video.ObjectKey,
			AlarmInfo:      alarmInfo,
			UpdatedAt:      video.UpdatedAt,
			CreatedAt:      video.CreatedAt,
			IsDeleted:      video.IsDeleted,
			Url:            warnVideosUrlMap[video.ID],
		}
	}).([]dto.WarnVideoResp)

	return map[string]interface{}{
		"job_record_info":  jobRecord,
		"job_logs":         jobLogsResp,
		"job_videos":       jobVideosResp,
		"job_pic":          jobMediaResp,
		"job_warn_records": jobWarnRecordResp,
		"job_warn_videos":  jobWarnVideosResp,
	}, nil
}

func (s *InspectionJobRecordService) Delete(id int) error {
	// 以下是桥梁、接触网数据
	if funk.ContainsInt([]int{1147, 1148, 1149, 1150}, id) {
		err := s.db.Where("id = ?", id).Delete(&model.InspectionJobRecord{}).Error
		if err != nil {
			return err
		}

		return nil
	}

	var jobRecord model.InspectionJobRecord
	err := s.db.Model(model.InspectionJobRecord{}).Where("id = ?", id).First(&jobRecord).Error
	if err != nil {
		return err
	}

	tx := s.db.Begin()

	err = tx.Where("id = ?", id).Delete(&model.InspectionJobRecord{}).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	if jobRecord.Type == 1 {
		err = tx.Where("job_id = ?", jobRecord.ManualJobID).Delete(&model.ManualJob{}).Error
		if err != nil {
			tx.Rollback()
			return err
		}
	} else if jobRecord.Type == 2 {
		err = tx.Where("job_id = ?", jobRecord.WaylineJobID).Delete(&model.WaylineJob{}).Error
		if err != nil {
			tx.Rollback()
			return err
		}
	}

	err = tx.Where("job_record_id = ?", id).Delete(&model.JobLogs{}).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	err = tx.Where("job_record_id = ?", id).Delete(&model.JobWarnRecord{}).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	err = tx.Where("job_record_id = ?", id).Delete(&model.JobWarn{}).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	mediaList := make([]model.MediaFile, 0)
	opMedia := s.db.Model(model.MediaFile{})
	if jobRecord.Type == 1 {
		opMedia.Where("manual_job_id = ?", jobRecord.ManualJobID)
	} else if jobRecord.Type == 2 {
		opMedia.Where("wayline_job_id = ?", jobRecord.WaylineJobID)
	}
	if err = opMedia.Find(&mediaList).Error; err != nil {
		tx.Rollback()
		return err
	}

	for _, media := range mediaList {
		if err = tx.Delete(&model.MediaFile{}, media.ID).Error; err != nil {
			tx.Rollback()
			return err
		}
		if err = s.ossService.DelObject(media.ObjectKey); err != nil {
			tx.Rollback()
			return err
		}
	}

	warnVideoList := make([]model.WarnVideo, 0)
	err = tx.Model(model.WarnVideo{}).Where("job_record_id = ?", id).Find(&warnVideoList).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	for _, video := range warnVideoList {
		if err = tx.Delete(&model.WarnVideo{}, video.ID).Error; err != nil {
			tx.Rollback()
			return err
		}
		if err = s.ossService.DelObject(video.ObjectKey); err != nil {
			tx.Rollback()
			return err
		}
	}

	err = NewWaylineRedisService().delDroneAttitudeInfo(jobRecord.WaylineJobID)
	if err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()

	if jobRecord.TaskTrajectoryPic != "" {
		err = s.ossService.DelObject(jobRecord.TaskTrajectoryPic)
		if err != nil {
			s.logger.Errorf("failed to delete task trajectory pic, id: %d, err: %v", id, err)
		}
	}

	return nil
}

func (s *InspectionJobRecordService) GetById(id int) (*model.InspectionJobRecord, error) {
	var data model.InspectionJobRecord
	err := s.db.Model(model.InspectionJobRecord{}).Where("id = ?", id).First(&data).Error
	if err != nil {
		return nil, err
	}

	return &data, nil
}

func (s *InspectionJobRecordService) getByManualJobId(manualJobID string) (*model.InspectionJobRecord, error) {
	var data model.InspectionJobRecord
	err := s.db.Model(model.InspectionJobRecord{}).Where("manual_job_id = ?", manualJobID).First(&data).Error
	if err != nil {
		return nil, err
	}

	return &data, nil
}

func (s *InspectionJobRecordService) getByWaylineJobId(waylineJobID string) (*model.InspectionJobRecord, error) {
	var data model.InspectionJobRecord
	err := s.db.Model(model.InspectionJobRecord{}).Where("wayline_job_id = ?", waylineJobID).First(&data).Error
	if err != nil {
		return nil, err
	}

	return &data, nil
}

func (s *InspectionJobRecordService) SaveTaskTrajectoryById(id int, data string) error {
	var trajectoryList []dto.Trajectory
	if err := json.Unmarshal([]byte(data), &trajectoryList); err != nil {
		return err
	}

	updateColumn := model.InspectionJobRecord{
		TaskTrajectory: data,
	}

	listLength := len(trajectoryList)

	if listLength > 0 {
		var jobRecord model.InspectionJobRecord
		err := s.db.Model(model.InspectionJobRecord{}).Where("id = ?", id).First(&jobRecord).Error
		if err != nil {
			return err
		}

		flightMileage := jobRecord.FlightMileage
		trajectoryCalculateIndex := jobRecord.TrajectoryCalculateIndex

		if trajectoryCalculateIndex < listLength {
			for i := jobRecord.TrajectoryCalculateIndex; i < listLength; i++ {
				if i == 0 {
					continue
				}

				// 计算与上个坐标的距离
				dis := utils.GetDistanceInMeter(trajectoryList[i-1].Standard.Lat, trajectoryList[i-1].Standard.Lng, trajectoryList[i].Standard.Lat, trajectoryList[i].Standard.Lng)
				flightMileage += dis

				trajectoryCalculateIndex++
			}

			// 将索引设置为下次计算的开始索引
			updateColumn.TrajectoryCalculateIndex = trajectoryCalculateIndex
			updateColumn.FlightMileage = flightMileage

			// 更新任务进度
			if jobRecord.ExpectedFlightMileage > 0 && jobRecord.Percent < 99 {
				percent := int(math.Floor(flightMileage / jobRecord.ExpectedFlightMileage * 100))
				if percent > 99 {
					percent = 99
				}

				updateColumn.Percent = percent

				//s.logger.Debugf("percent: %d, current flightMileage: %v, expectedFlightMileage: %v, listLength: %v", percent, flightMileage, jobRecord.ExpectedFlightMileage, listLength)
			}
		}
	}

	err := s.db.Model(model.InspectionJobRecord{}).Where("id = ?", id).Updates(updateColumn).Error
	if err != nil {
		return err
	}

	return nil
}

func (s *InspectionJobRecordService) EndJob(id int, isErr bool) error {
	jobRecord, err := s.GetById(id)
	if err != nil {
		return err
	}

	if jobRecord.Type == 1 {
		manualJobSvc := NewManualJobService()

		err = manualJobSvc.endManualJob(jobRecord.ManualJobID)
		if err != nil {
			return err
		}

		var status enum.ManualJobStatusEnum
		if isErr {
			status = enum.ManualJob_FAILED
		} else {
			status = enum.ManualJob_Success
		}

		err = manualJobSvc.UpdateManualJobStatus(status, jobRecord.ManualJobID)
		if err != nil {
			return err
		}
	} else if jobRecord.Type == 2 {
		waylineJobSvc := NewWaylineJobService()

		var waylineJob *model.WaylineJob
		waylineJob, err = waylineJobSvc.GetWaylineJobByJobId(jobRecord.WaylineJobID)
		if err != nil {
			return err
		}

		dockSN := waylineJob.DockSN
		jobID := waylineJob.JobID

		err = waylineJobSvc.WaylineJobWorkDone(dockSN, jobID)
		if err != nil {
			return err
		}

		var status enum.WaylineJobStatusEnum
		if isErr {
			status = enum.FAILED
		} else {
			status = enum.Success
		}

		err = waylineJobSvc.UpdateWaylineJobStatus(status.Value(), jobID, nil)
		if err != nil {
			return err
		}
	}

	//droneSvc := NewDroneService()
	//droneSN := waylineJob.DroneSN

	//droneOnline, _ := NewDeviceRedisService().CheckDeviceOnline(dockSN)
	//
	//var workStatus int
	//
	//if droneOnline {
	//	workStatus = Drone_Work_Status_IDLE
	//} else {
	//	workStatus = Drone_Work_Status_Offline
	//}
	//
	//err = droneSvc.updateWorkStatus(workStatus, droneSN)
	//if err != nil {
	//	s.logger.Errorf("修改无人机work_status失败: %s", err.Error())
	//	return err
	//}

	return nil
}

func (s *InspectionJobRecordService) SaveTaskTrajectoryPic(jobRecord model.InspectionJobRecord, picObjectKey string) error {
	err := s.db.Model(model.InspectionJobRecord{}).Where("id = ?", jobRecord.ID).Update("task_trajectory_pic", picObjectKey).Error
	if err != nil {
		return err
	}
	return nil
}

func (s *InspectionJobRecordService) GenReport(id int) error {
	jobRecord, err := s.GetById(id)
	if err != nil {
		return err
	}

	var points []dto.KMLPoint
	err = json.Unmarshal([]byte(jobRecord.TargetTrajectory), &points)
	if err != nil {
		s.logger.Errorf("GenReport: failed to decode target trajectory, err: %v", err)
		return err
	}

	warnRecords, err := NewJobWarnRecordService().getWarnRecordByJobRecordId(id)
	if err != nil {
		return err
	}

	oldReportDocxObjectKey := jobRecord.ReportDocxObjectKey

	warnRecordDtoList, err := NewJobWarnRecordService().convertWarnRecordToDto(warnRecords)
	if err != nil {
		return err
	}

	var docxObjectKey string
	docxObjectKey, err = getReport(*jobRecord, warnRecordDtoList, points)
	if err != nil {
		s.logger.Errorf("GenReport: failed to get report, err: %v", err)
		return err
	}

	columns := map[string]interface{}{
		"report_docx_object_key": docxObjectKey,
	}

	err = s.db.Model(model.InspectionJobRecord{}).Where("id = ?", jobRecord.ID).Updates(columns).Error
	if err != nil {
		s.logger.Errorf("GenReport: failed to set report_object_key, err: %v", err)
		return err
	}

	if oldReportDocxObjectKey != "" {
		if err = s.ossService.DelObject(oldReportDocxObjectKey); err != nil {
			return err
		}
	}

	return nil
}

// handleUnclosedTasks  处理未关闭的任务
func (s *InspectionJobRecordService) handleUnclosedTasks() {
	if CheckIsOccupiedPermissions() {
		return
	}

	jobLogsSvc := NewJobLogsService()
	waylineJobSvc := NewWaylineJobService()

	// 距现在超过45分钟的任务
	ago := time.Now().Add(-45 * time.Minute)

	jobRecordList := make([]model.InspectionJobRecord, 0)
	err := s.db.Model(model.InspectionJobRecord{}).
		Where("type = ?", 2).
		Where("status in (?)", []int{InspectionJobStatusWorking, InspectionJobStatusPAUSED}).
		Where("execute_time < ?", ago).
		Where("NOT EXISTS (SELECT 1 FROM device WHERE device.job_id = inspection_job_record.wayline_job_id)").
		Find(&jobRecordList).
		Error

	if err != nil {
		s.logger.Errorf("handleUnclosedTasks: failed to get jobRecordList, err: %v", err)
		return
	}

	for _, record := range jobRecordList {
		err = s.EndJob(record.ID, true)
		if err != nil {
			s.logger.Errorf("handleUnclosedTasks: failed to end job, err: %v", err)
			continue
		}

		var waylineJob *model.WaylineJob
		waylineJob, err = waylineJobSvc.GetWaylineJobByJobId(record.WaylineJobID)
		if err != nil {
			s.logger.Errorf("handleUnclosedTasks: failed to get wayline job, err: %v", err)
			continue
		}

		err = jobLogsSvc.SaveJobLogByGateway(enum.JobTypeEnum_Wayline, record.WaylineJobID, waylineJob.DockSN, "任务异常结束", true)
		if err != nil {
			s.logger.Errorf("handleUnclosedTasks: failed to save job log, err: %v", err)
		}
	}
}

func (s *InspectionJobRecordService) GetJobTrajectory(id int) (dto.JobTrajectoryResp, error) {
	jobRecord, err := s.GetById(id)
	if err != nil {
		return dto.JobTrajectoryResp{}, err
	}

	var trajectoryList []dto.Trajectory
	if jobRecord.TaskTrajectory != "" {
		if err = json.Unmarshal([]byte(jobRecord.TaskTrajectory), &trajectoryList); err != nil {
			return dto.JobTrajectoryResp{}, err
		}
	}

	var targetTrajectory []dto.KMLPoint
	if jobRecord.TargetTrajectory != "" {
		if err = json.Unmarshal([]byte(jobRecord.TargetTrajectory), &targetTrajectory); err != nil {
			return dto.JobTrajectoryResp{}, err
		}
	}

	return dto.JobTrajectoryResp{
		Type:             jobRecord.Type,
		TaskTrajectory:   trajectoryList,
		TargetTrajectory: targetTrajectory,
	}, nil
}

func (s *InspectionJobRecordService) UpdateProcessedStatus(id int, status enum.JobProcessedStatusEnum) error {
	err := s.db.Model(model.InspectionJobRecord{}).Where("id = ?", id).Update("processed_status", status.Value()).Error
	if err != nil {
		return err
	}
	return nil
}

// JobRecordWorker 任务记录的后台工作协程
func JobRecordWorker() {
	service := NewInspectionJobRecordService()
	go func() {
		ticker := time.NewTicker(5 * time.Minute)
		for {
			service.handleUnclosedTasks()
			<-ticker.C
		}
	}()
}

// isJobProcessedStatusToPending 判断任务的算法处理状态是否需要更新为pending
func isJobProcessedStatusToPending(cid int, scene enum.JobSceneTypeEnum, fileType enum.JobProcessedFileType) bool {
	flag := false

	if scene == enum.JobSceneTypeEnum_SubwayGround {
		// 地体地保
		if cid == 1 || cid == 91 || cid == 94 {
			//if cid == 1 || cid == 91 {
			// 福州、厦门用云台拍摄视频
			if fileType == enum.JobProcessedFileType_CameraTake || fileType == enum.JobProcessedFileType_CameraTakeVideo {
				flag = true
			}
		} else {
			// 其他平台都用直播录制视频
			if fileType == enum.JobProcessedFileType_LiveRecord {
				flag = true
			}
		}
	} else if scene == enum.JobSceneTypeEnum_Bridge || scene == enum.JobSceneTypeEnum_Road {
		// 桥梁、路面
		if fileType == enum.JobProcessedFileType_CameraTake {
			// 用云台拍摄照片
			flag = true
		}
	}

	return flag
}
