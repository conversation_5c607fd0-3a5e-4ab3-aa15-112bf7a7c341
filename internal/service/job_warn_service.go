package service

import (
	"bytes"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"sync"
	"time"
	"wukong-api/internal/config"
	"wukong-api/internal/dto"
	"wukong-api/internal/enum"
	"wukong-api/internal/model"
	"wukong-api/internal/repo"

	"github.com/qxsugar/pkg/apix"

	"github.com/qxsugar/pkg/dbx"
	"github.com/thoas/go-funk"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	jobWarnServiceOnce    sync.Once
	jobWarnServiceService *JobWarnService
)

type JobWarnService struct {
	db     *gorm.DB
	logger *zap.SugaredLogger
	config config.Config
}

func NewJobWarnService() *JobWarnService {
	jobWarnServiceOnce.Do(func() {
		jobWarnServiceService = &JobWarnService{
			db:     repo.GetDatabase(),
			logger: repo.GetLogger(),
			config: config.GetConfig(),
		}
	})
	return jobWarnServiceService
}

func (s *JobWarnService) List(params dto.JobWarnListReq, cid int, isLatest bool) (*apix.PageBody, error) {
	count := int64(0)
	warns := make([]model.JobWarn, 0)

	op := s.db.Model(&model.JobWarn{})

	if cid == 1 {
		op = op.Where("company_id = ? OR id IN (17, 21, 22, 23)", cid)
	} else {
		op = op.Where("company_id = ?", cid)
	}

	op = op.Order("warn_at desc")

	if params.StartTime != 0 {
		op.Where("warn_at >= ?", time.Unix(params.StartTime, 0))
	}
	if params.EndTime != 0 {
		op.Where("warn_at <= ?", time.Unix(params.EndTime, 0))
	}
	if params.WarnType != "" {
		condition := `%"` + params.WarnType + `"%`
		op.Where("warn_type = ?", condition)
	}
	if params.Level != 0 {
		op.Where("level = ?", params.Level)
	}
	if params.Status != 0 {
		op.Where("status = ?", params.Status)
	}
	if params.Name != "" {
		condition := dbx.LikeString(params.Name)
		op = op.Where("warn_type like ?", condition)
	}
	if params.ID != 0 {
		op.Where("id = ?", params.ID)
	}

	if isLatest {
		subQuery := s.db.Model(&model.JobWarn{}).
			Select("MAX(job_record_id) as max_job_record_id").
			Where("company_id = ?", cid)

		op = op.Where("job_record_id = (?)", subQuery)
	} else {
		op = op.Count(&count).Offset(params.Offset).Limit(params.Limit)
	}

	err := op.Scan(&warns).Error
	if err != nil {
		return nil, err
	}

	warnIds := funk.Map(warns, func(warn model.JobWarn) int {
		return warn.ID
	}).([]int)

	var warnRecords []model.JobWarnRecord

	recordOp := s.db.Model(&model.JobWarnRecord{}).
		Where("job_warn_id IN ?", warnIds).
		Order("warn_at desc")

	err = recordOp.Find(&warnRecords).Error
	if err != nil {
		return nil, err
	}

	warnRecordsRespList, err := NewJobWarnRecordService().getJobWarnRecordRespListByJobWarnRecords(warnRecords)
	if err != nil {
		return nil, err
	}

	groupedRecords := make(map[int][]dto.JobWarnRecordResp)
	for _, record := range warnRecordsRespList {
		groupedRecords[record.JobWarnID] = append(groupedRecords[record.JobWarnID], record)
	}

	respList := funk.Map(warns, func(warn model.JobWarn) dto.JobWarnListResp {
		var warnType []string
		err = json.Unmarshal([]byte(warn.WarnType), &warnType)
		if err != nil {
			s.logger.Errorf("failed to unmarshal warn type, warn id: %d, err: %v", warn.ID, err)
		}

		var positionList []dto.JobWarnPosition
		for _, record := range groupedRecords[warn.ID] {
			positionList = append(positionList, dto.JobWarnPosition{
				WarnRecordID: record.ID,
				WarnDesc:     record.WarnDesc,
				Bd09ll:       record.Position.Bd09ll,
				Standard:     record.Position.Standard,
			})
		}

		return dto.JobWarnListResp{
			ID:             warn.ID,
			CompanyID:      warn.CompanyID,
			JobRecordID:    warn.JobRecordID,
			JobRecordName:  warn.JobRecordName,
			WarnType:       warnType,
			Area:           warn.Area,
			Level:          warn.Level,
			WarnAt:         warn.WarnAt,
			Status:         warn.Status,
			WarnRecordList: groupedRecords[warn.ID],
			PositionList:   positionList,
			UserID:         warn.UserID,
		}
	}).([]dto.JobWarnListResp)

	return &apix.PageBody{
		Offset: params.Offset,
		Limit:  params.Limit,
		Total:  count,
		List:   respList,
	}, nil
}

// 大模型检测
func (s *JobWarnService) ModelCheck(params dto.JobWarnModelCheckReq, cid int) (interface{}, error) {
	results := make([]map[string]interface{}, 0)

	// 创建warnId到imageBase64的映射
	warnIdToImageMap := make(map[int]string)
	for _, check := range params.Checks {
		warnIdToImageMap[check.ID] = check.ImageBase64
	}
	// 获取所有warnId
	warnIds := make([]int, 0, len(params.Checks))
	for _, check := range params.Checks {
		warnIds = append(warnIds, check.ID)
	}

	// 批量查询JobWarnRecord
	var warnRecords []model.JobWarnRecord
	err := s.db.Where("id IN ?", warnIds).Find(&warnRecords).Error
	if err != nil {
		s.logger.Errorf("ModelCheck: failed to get job warn records, err: %v", err)
		return nil, apix.NewInvalidArgumentError().WithErr(err)
	}
	// 分离需要请求的和已有结果的
	needRequestWarnIds := make([]int, 0)
	existingResults := make(map[int]string)

	for _, record := range warnRecords {
		if record.ModelCheck == "" {
			needRequestWarnIds = append(needRequestWarnIds, record.ID)
		} else {
			existingResults[record.ID] = record.ModelCheck
		}
	}
	// 并发处理需要请求的记录
	newResults := make(map[int]string)
	if len(needRequestWarnIds) > 0 {
		// 使用channel和sync.WaitGroup来处理并发
		type arkResult struct {
			warnId int
			result string
			err    error
		}

		resultChan := make(chan arkResult, len(needRequestWarnIds))
		var wg sync.WaitGroup

		// 启动goroutine并发请求
		for _, warnId := range needRequestWarnIds {
			wg.Add(1)
			imageBase64 := warnIdToImageMap[warnId]
			s.logger.Infof("准备发送arkReq请求: warnId=%d, imageBase64长度=%d", warnId, len(imageBase64))
			go func(id int, imageBase64 string) {
				defer wg.Done()

				modelCheckResp, err := s.arkReq(imageBase64)
				if err != nil {
					s.logger.Errorf("ModelCheck: arkReq failed, warnId: %d, err: %v", id, err)
					resultChan <- arkResult{warnId: id, result: "", err: errors.New("模型token余额不足或密钥错误")}
					return
				}

				// arkReq现在直接返回content字符串，无需再序列化
				contentStr, ok := modelCheckResp.(string)
				if !ok {
					s.logger.Errorf("ModelCheck: arkReq response is not string, warnId: %d", id)
					resultChan <- arkResult{warnId: id, result: "", err: errors.New("大模型返回类型错误")}
					return
				}

				resultChan <- arkResult{warnId: id, result: contentStr, err: nil}
			}(warnId, imageBase64)
		}

		// 等待所有goroutine完成
		// go func() {

		// }()
		wg.Wait()
		close(resultChan)
		// 收集结果
		for result := range resultChan {
			if result.err != nil {
				// 如果有错误，直接返回
				return nil, apix.NewInvalidArgumentError().WithErr(result.err).WithMsg(result.err.Error())
			}
			newResults[result.warnId] = result.result
		}

		// 批量更新数据库
		if len(newResults) > 0 {
			// 使用事务批量更新
			tx := s.db.Begin()
			for warnId, modelCheck := range newResults {
				err = tx.Model(&model.JobWarnRecord{}).
					Where("id = ?", warnId).
					Update("model_check", modelCheck).Error
				if err != nil {
					tx.Rollback()
					s.logger.Errorf("ModelCheck: failed to update model_check, warnId: %d, err: %v", warnId, err)
					return nil, apix.NewInvalidArgumentError().WithMsg("模型token余额不足或密钥错误")
				}
			}

			if err = tx.Commit().Error; err != nil {
				s.logger.Errorf("ModelCheck: failed to commit transaction, err: %v", err)
				return nil, apix.NewInvalidArgumentError().WithErr(err)
			}
		}
	}
	// 合并所有结果
	allResults := make(map[int]string)
	// 添加已存在的结果
	for warnId, result := range existingResults {
		allResults[warnId] = result
	}
	// 添加新请求的结果
	for warnId, result := range newResults {
		allResults[warnId] = result
	}

	// 构建返回结果
	for _, check := range params.Checks {
		result := map[string]interface{}{
			"id":          check.ID,
			"model_check": allResults[check.ID],
		}
		results = append(results, result)
	}

	return map[string]interface{}{
		"results": results,
	}, nil
}

func (s *JobWarnService) Overview(cid int) (interface{}, error) {
	var stats dto.WarnStatistics

	// 使用单次查询获取所有状态的计数
	type StatusCount struct {
		Status enum.JobWarnStatusEnum
		Count  int64
	}
	var statusCounts []StatusCount
	err := s.db.Model(&model.JobWarn{}).
		Select("status, count(*) as count").
		Where("company_id = ?", cid).
		Group("status").
		Scan(&statusCounts).Error
	if err != nil {
		return nil, err
	}

	// 处理状态计数
	stats.TotalCount = 0
	for _, sc := range statusCounts {
		stats.TotalCount += sc.Count
		switch sc.Status {
		case enum.Processed:
			stats.ProcessedCount = sc.Count
		case enum.NotProcessed:
			stats.UnProcessCount = sc.Count
		case enum.Ignored:
			stats.IgnoredCount = sc.Count
		case enum.Processing:
			stats.ProcessingCount = sc.Count
		}
	}

	// 计算平均处理时间
	err = s.db.Model(&model.JobWarn{}).
		Select("COALESCE(ROUND(AVG(UNIX_TIMESTAMP(handle_at) - UNIX_TIMESTAMP(created_at))/3600, 2), 0)").
		Where("company_id = ? AND status = ?", cid, enum.Processed).
		Scan(&stats.AvgProcessHours).
		Error
	if err != nil {
		return nil, err
	}

	warns := make([]model.JobWarn, 0)
	err = s.db.Model(&model.JobWarn{}).Where("company_id = ?", cid).Find(&warns).Error
	if err != nil {
		return nil, err
	}

	typeCountMap := make(map[string]int)

	for _, record := range warns {
		var warnTypes []string
		if err = json.Unmarshal([]byte(record.WarnType), &warnTypes); err != nil {
			return nil, err
		}

		for _, warnType := range warnTypes {
			typeCountMap[warnType]++
		}
	}

	var typeCounts []dto.TypeCount
	for warnType, count := range typeCountMap {
		typeCounts = append(typeCounts, dto.TypeCount{WarnType: warnType, Count: count})
	}

	return dto.JobWarnRecordOverviewResp{
		WarnStatistics: stats,
		TypeCount:      typeCounts,
	}, nil
}

func (s *JobWarnService) ListByAdmin(params dto.JobWarnByAdminReq, cid int) (*apix.PageBody, error) {
	count := int64(0)
	warns := make([]model.JobWarn, 0)

	op := s.db.Model(&model.JobWarn{})

	op.Where("company_id = ?", cid)

	if params.Status != 0 {
		op = op.Where("status = ?", params.Status)
	}

	if params.Name != "" {
		condition := dbx.LikeString(params.Name)
		op = op.Where("warn_type like ?", condition)
	}

	// 根据排序参数添加排序
	if params.SortBy == "level" {
		if params.IsDesc {
			op = op.Order("level desc")
		} else {
			op = op.Order("level asc")
		}
	} else {
		if params.IsDesc {
			op = op.Order("warn_at desc")
		} else {
			op = op.Order("warn_at asc")
		}
	}

	op = op.Count(&count).Offset(params.Offset).Limit(params.Limit)

	err := op.Scan(&warns).Error
	if err != nil {
		return nil, err
	}

	warnIds := funk.Map(warns, func(warn model.JobWarn) int {
		return warn.ID
	}).([]int)

	var warnRecords []model.JobWarnRecord

	recordOp := s.db.Model(&model.JobWarnRecord{}).
		Where("job_warn_id IN ?", warnIds).
		Order("warn_at desc")

	err = recordOp.Find(&warnRecords).Error
	if err != nil {
		return nil, err
	}

	warnRecordsRespList, err := NewJobWarnRecordService().getJobWarnRecordRespListByJobWarnRecords(warnRecords)
	if err != nil {
		return nil, err
	}

	groupedRecords := make(map[int][]dto.JobWarnRecordResp)
	for _, record := range warnRecordsRespList {
		groupedRecords[record.JobWarnID] = append(groupedRecords[record.JobWarnID], record)
	}

	respList := funk.Map(warns, func(warn model.JobWarn) dto.JobWarnListResp {
		var warnType []string
		err = json.Unmarshal([]byte(warn.WarnType), &warnType)
		if err != nil {
			s.logger.Errorf("failed to unmarshal warn type, warn id: %d, err: %v", warn.ID, err)
		}

		var positionList []dto.JobWarnPosition
		for _, record := range groupedRecords[warn.ID] {
			positionList = append(positionList, dto.JobWarnPosition{
				WarnRecordID: record.ID,
				WarnDesc:     record.WarnDesc,
				Bd09ll:       record.Position.Bd09ll,
				Standard:     record.Position.Standard,
			})
		}

		return dto.JobWarnListResp{
			ID:             warn.ID,
			CompanyID:      warn.CompanyID,
			JobRecordID:    warn.JobRecordID,
			JobRecordName:  warn.JobRecordName,
			WarnType:       warnType,
			Area:           warn.Area,
			Level:          warn.Level,
			WarnAt:         warn.WarnAt,
			Status:         warn.Status,
			WarnRecordList: groupedRecords[warn.ID],
			PositionList:   positionList,
		}
	}).([]dto.JobWarnListResp)

	return &apix.PageBody{
		Offset: params.Offset,
		Limit:  params.Limit,
		Total:  count,
		List:   respList,
	}, nil
}

func (s *JobWarnService) ListByUser(params dto.JobWarnByUserReq, userID int) (*apix.PageBody, error) {
	count := int64(0)
	warns := make([]model.JobWarn, 0)

	op := s.db.Model(&model.JobWarn{})

	op.Where("user_id = ?", userID)

	if params.Status != 0 {
		op = op.Where("status = ?", params.Status)
	}

	// 根据排序参数添加排序
	if params.SortBy == "level" {
		if params.IsDesc {
			op = op.Order("level desc")
		} else {
			op = op.Order("level asc")
		}
	} else {
		if params.IsDesc {
			op = op.Order("warn_at desc")
		} else {
			op = op.Order("warn_at asc")
		}
	}

	op = op.Count(&count).Offset(params.Offset).Limit(params.Limit)

	err := op.Scan(&warns).Error
	if err != nil {
		return nil, err
	}

	warnIds := funk.Map(warns, func(warn model.JobWarn) int {
		return warn.ID
	}).([]int)

	var warnRecords []model.JobWarnRecord

	recordOp := s.db.Model(&model.JobWarnRecord{}).
		Where("job_warn_id IN ?", warnIds).
		Order("warn_at desc")

	err = recordOp.Find(&warnRecords).Error
	if err != nil {
		return nil, err
	}

	warnRecordsRespList, err := NewJobWarnRecordService().getJobWarnRecordRespListByJobWarnRecords(warnRecords)
	if err != nil {
		return nil, err
	}

	groupedRecords := make(map[int][]dto.JobWarnRecordResp)
	for _, record := range warnRecordsRespList {
		groupedRecords[record.JobWarnID] = append(groupedRecords[record.JobWarnID], record)
	}

	respList := funk.Map(warns, func(warn model.JobWarn) dto.JobWarnListResp {
		var warnType []string
		err = json.Unmarshal([]byte(warn.WarnType), &warnType)
		if err != nil {
			s.logger.Errorf("failed to unmarshal warn type, warn id: %d, err: %v", warn.ID, err)
		}

		var positionList []dto.JobWarnPosition
		for _, record := range groupedRecords[warn.ID] {
			positionList = append(positionList, dto.JobWarnPosition{
				WarnRecordID: record.ID,
				WarnDesc:     record.WarnDesc,
				Bd09ll:       record.Position.Bd09ll,
				Standard:     record.Position.Standard,
			})
		}

		return dto.JobWarnListResp{
			ID:             warn.ID,
			CompanyID:      warn.CompanyID,
			JobRecordID:    warn.JobRecordID,
			JobRecordName:  warn.JobRecordName,
			WarnType:       warnType,
			Area:           warn.Area,
			Level:          warn.Level,
			WarnAt:         warn.WarnAt,
			Status:         warn.Status,
			WarnRecordList: groupedRecords[warn.ID],
			PositionList:   positionList,
		}
	}).([]dto.JobWarnListResp)

	return &apix.PageBody{
		Offset: params.Offset,
		Limit:  params.Limit,
		Total:  count,
		List:   respList,
	}, nil
}

func (s *JobWarnService) UpdateStatus(params dto.JobWarnUpdateStatusReq, cid int) (interface{}, error) {
	op := s.db.Model(&model.JobWarn{}).Where("id = ?", params.ID).Where("company_id = ?", cid)

	op.Update("status", params.Status)

	if err := op.Error; err != nil {
		return nil, err
	}

	return nil, nil
}

func (s *JobWarnService) Delete(params dto.JobWarnDeleteReq) error {
	return s.db.Where("id = ?", params.ID).Delete(&model.JobWarn{}).Error
}

// CheckPointInProtectedArea 检查指定的经纬度是否在保护区域内
// 如果点位于多个区域内，返回最小的level
func CheckPointInProtectedArea(areas []dto.ProtectedArea, lng, lat float64) *int {
	var minLevel *int

	for _, area := range areas {
		for _, polygon := range area.Points {
			if isPointInPolygon(lng, lat, polygon) {
				if minLevel == nil || area.Level < *minLevel {
					minLevel = &area.Level
				}
			}
		}
	}

	return minLevel
}

// isPointInPolygon 使用射线投射算法判断点是否在多边形内
// 参数：lng, lat 为待检测点的经纬度，polygon 为多边形的顶点数组
func isPointInPolygon(px, py float64, polygon []struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}) bool {
	if len(polygon) < 3 {
		return false // 多边形至少需要3个点
	}

	isInside := false
	const ESP = 1e-9 // 无限小的正数
	count := 0

	// 创建一个从目标点水平向右的射线
	linePoint1x := px
	linePoint1y := py
	linePoint2x := 180.0 // 射线终点设为180度（足够远）
	linePoint2y := py

	for i := 0; i < len(polygon)-1; i++ {
		cx1 := polygon[i].Lng
		cy1 := polygon[i].Lat
		cx2 := polygon[i+1].Lng
		cy2 := polygon[i+1].Lat

		// 如果目标点在任何一条边上，直接返回true
		if isPointOnLine(px, py, cx1, cy1, cx2, cy2) {
			return true
		}

		// 如果线段的长度无限小(趋于零)那么这两点实际是重合的，不足以构成一条线段
		if abs(cy2-cy1) < ESP {
			continue
		}

		// 第一个点是否在以目标点为基础衍生的平行纬度线上
		if isPointOnLine(cx1, cy1, linePoint1x, linePoint1y, linePoint2x, linePoint2y) {
			// 第二个点在第一个的下方,靠近赤道纬度为零(最小纬度)
			if cy1 > cy2 {
				count++
			}
		} else if isPointOnLine(cx2, cy2, linePoint1x, linePoint1y, linePoint2x, linePoint2y) {
			// 第二个点在第一个的上方,靠近极点(南极或北极)纬度为90(最大纬度)
			if cy2 > cy1 {
				count++
			}
		} else if isIntersect(cx1, cy1, cx2, cy2, linePoint1x, linePoint1y, linePoint2x, linePoint2y) {
			// 由两点组成的线段是否和以目标点为基础衍生的平行纬度线相交
			count++
		}
	}

	// 如果交点数为奇数，点在多边形内
	if count%2 == 1 {
		isInside = true
	}

	return isInside
}

// abs 计算浮点数的绝对值
func abs(x float64) float64 {
	if x < 0 {
		return -x
	}
	return x
}

// multiply 计算叉积
func multiply(px0, py0, px1, py1, px2, py2 float64) float64 {
	return ((px1-px0)*(py2-py0) - (px2-px0)*(py1-py0))
}

// isPointOnLine 判断目标点是否在目标线段上
func isPointOnLine(px0, py0, px1, py1, px2, py2 float64) bool {
	const ESP = 1e-9 // 无限小的正数

	if (abs(multiply(px0, py0, px1, py1, px2, py2)) < ESP) &&
		((px0-px1)*(px0-px2) <= 0) &&
		((py0-py1)*(py0-py2) <= 0) {
		return true
	}
	return false
}

// isIntersect 判断是否有横断（两条线段是否相交）
func isIntersect(px1, py1, px2, py2, px3, py3, px4, py4 float64) bool {
	d := (px2-px1)*(py4-py3) - (py2-py1)*(px4-px3)
	if d != 0 {
		r := ((py1-py3)*(px4-px3) - (px1-px3)*(py4-py3)) / d
		s := ((py1-py3)*(px2-px1) - (px1-px3)*(py2-py1)) / d
		if (r >= 0) && (r <= 1) && (s >= 0) && (s <= 1) {
			return true
		}
	}
	return false
}

type ArkRequest struct {
	Model    string        `json:"model"`
	Messages []interface{} `json:"messages"`
}

// 大模型请求
func (s *JobWarnService) arkReq(ImageBase64 string) (interface{}, error) {
	arkApiKey := s.config.AI.ApiKey
	if arkApiKey == "" {
		return nil, errors.New("缺少 ARK_API_KEY")
	}
	// 构造豆包请求
	arkReq := ArkRequest{
		Model: "doubao-seed-1-6-250615",
		Messages: []interface{}{
			map[string]interface{}{
				"role": "user",
				"content": []interface{}{
					map[string]interface{}{
						"type": "image_url",
						"image_url": map[string]interface{}{
							"url": ImageBase64,
						},
					},
					map[string]interface{}{
						"type": "text",
						"text": `你是专业的无人机巡检分析助手，请严格按照以下要求生成简洁且结构化的分析报告，字数限制在150字以内：

以下是巡检关注的场景列表,识别当前异常场景：

    桥梁相关: 桥梁附近施工工地
	地保相关: 地铁附近施工工地

请严格遵守以下规则：

1. 仅识别图片中的场景，分析图片中是否存在未标注出的潜在风险场景.

2. 结合目标物列表，仔细分析图片中是否存在风险场景.

3. 结合当前巡检业务场景（地保、桥梁），详细分析各目标的场景影响。

4. 综合图片巡检背景，分析其他潜在风险和可识别目标，并简要说明。

5. **请严格只输出以下结构化内容，且不得添加任何其他信息、说明或数字：**

格式：

- 告警分析：(识别到工地/未识别到工地)-具体描述及分析
- 场景影响：目标在当前场景的影响及整体风险评估
- 潜在风险：未检测出的潜在风险场景，包含其他潜在风险分析

请务必完全遵守以上格式和内容限制,请重新输出。


`,
					},
				},
			},
		},
	}

	body, _ := json.Marshal(arkReq)

	// 添加调试信息
	s.logger.Infof("arkReq ImageBase64 长度: %d", len(ImageBase64))
	if len(ImageBase64) > 100 {
		s.logger.Infof("arkReq ImageBase64 前100字符: %s", ImageBase64[:100])
	} else {
		s.logger.Infof("arkReq ImageBase64 完整内容: %s", ImageBase64)
	}
	s.logger.Infof("arkReq 请求体: %s", string(body))

	// 创建不限制超时时间的HTTP客户端
	client := &http.Client{
		Timeout: 50 * time.Second, // 0表示不限制超时时间，等待请求完成
	}
	reqPost, _ := http.NewRequest("POST", "https://ark.cn-beijing.volces.com/api/v3/chat/completions", bytes.NewBuffer(body))
	reqPost.Header.Set("Content-Type", "application/json")
	reqPost.Header.Set("Authorization", "Bearer "+arkApiKey)

	resp, err := client.Do(reqPost)
	if err != nil {
		return nil, errors.New("上游模型请求失败：" + err.Error())
	}
	defer resp.Body.Close()
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, errors.New("读取响应失败：" + err.Error())
	}
	s.logger.Infof(">>>>>>>>>>>>>>>>>>>>>>>arkReq 响应体: %s", string(respBody))
	// 将[]byte类型的respBody转换为JSON对象
	var jsonResult map[string]interface{}
	err = json.Unmarshal(respBody, &jsonResult)
	if err != nil {
		return nil, errors.New("解析JSON响应失败：" + err.Error())
	}

	// 提取choices[0].message.content
	choices, ok := jsonResult["choices"].([]interface{})
	if !ok || len(choices) == 0 {
		return nil, errors.New("响应格式错误：缺少choices字段")
	}

	firstChoice, ok := choices[0].(map[string]interface{})
	if !ok {
		return nil, errors.New("响应格式错误：choices[0]格式不正确")
	}

	message, ok := firstChoice["message"].(map[string]interface{})
	if !ok {
		return nil, errors.New("响应格式错误：缺少message字段")
	}

	content, ok := message["content"].(string)
	if !ok {
		return nil, errors.New("响应格式错误：缺少content字段")
	}

	return content, nil
}
