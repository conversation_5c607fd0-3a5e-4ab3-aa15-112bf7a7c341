package service

import (
	"encoding/json"
	"sync"
	"time"
	"wukong-api/internal/dto"
	"wukong-api/internal/enum"
	"wukong-api/internal/model"
	"wukong-api/internal/repo"

	"github.com/qxsugar/pkg/apix"

	"github.com/qxsugar/pkg/dbx"
	"github.com/thoas/go-funk"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	jobWarnRecordServiceOnce    sync.Once
	jobWarnRecordServiceService *JobWarnRecordService
)

type JobWarnRecordService struct {
	db     *gorm.DB
	logger *zap.SugaredLogger
}

func NewJobWarnRecordService() *JobWarnRecordService {
	jobWarnRecordServiceOnce.Do(func() {
		jobWarnRecordServiceService = &JobWarnRecordService{
			db:     repo.GetDatabase(),
			logger: repo.GetLogger(),
		}
	})
	return jobWarnRecordServiceService
}

func (s *JobWarnRecordService) List(params dto.JobWarnRecordListReq, cid int, isLatest bool) (*apix.PageBody, error) {
	count := int64(0)
	warnRecords := make([]model.JobWarnRecord, 0)

	op := s.db.Model(&model.JobWarnRecord{}).Where("company_id = ?", cid).Order("warn_at desc")

	if params.StartTime != 0 {
		op.Where("warn_at >= ?", time.Unix(params.StartTime, 0))
	}
	if params.EndTime != 0 {
		op.Where("warn_at <= ?", time.Unix(params.EndTime, 0))
	}
	if params.WarnType != "" {
		condition := `%"` + params.WarnType + `"%`
		op.Where("warn_type = ?", condition)
	}
	if params.Level != 0 {
		op.Where("level = ?", params.Level)
	}
	if params.Status != 0 {
		op.Where("status = ?", params.Status)
	}
	if params.Name != "" {
		condition := dbx.LikeString(params.Name)
		op = op.Where("warn_desc like ?", condition)
	}
	if params.ID != 0 {
		op.Where("id = ?", params.ID)
	}
	if params.JobRecordID != 0 {
		op.Where("job_record_id = ?", params.JobRecordID)
	}

	if isLatest {
		subQuery := s.db.Model(&model.JobWarnRecord{}).
			Select("MAX(job_record_id) as max_job_record_id").
			Where("company_id = ?", cid)

		op = op.Where("job_record_id = (?)", subQuery)
	} else {
		op = op.Count(&count).Offset(params.Offset).Limit(params.Limit)
	}

	err := op.Scan(&warnRecords).Error
	if err != nil {
		return nil, err
	}

	respList, err := s.getJobWarnRecordRespListByJobWarnRecords(warnRecords)
	if err != nil {
		return nil, err
	}

	return &apix.PageBody{
		Limit:  params.Limit,
		Offset: params.Offset,
		Total:  count,
		List:   respList,
	}, nil
}

func (s *JobWarnRecordService) getJobWarnRecordRespListByJobWarnRecords(warnRecords []model.JobWarnRecord) ([]dto.JobWarnRecordResp, error) {
	warnRecordsUrlSyncMap := sync.Map{}
	var wg sync.WaitGroup

	warnRecordsObjectKeyDict := make(map[int][]string)
	for _, record := range warnRecords {
		var picList []string
		err := json.Unmarshal([]byte(record.PicList), &picList)
		if err != nil {
			return nil, err
		}

		warnRecordsObjectKeyDict[record.ID] = picList
	}

	for warnRecordId, objectKeyList := range warnRecordsObjectKeyDict {
		wg.Add(1)
		go func(id int, keyList []string) {
			defer wg.Done()

			urlList := make([]string, 0)
			for _, key := range keyList {
				url, err := NewOssService().GetObjectSignURL(key, int64(60*60*24))
				if err != nil {
					s.logger.Errorf("failed to get sign url,warnRecordId: %d, key: %s, err: %v", id, key, err)
					return
				}
				urlList = append(urlList, url)
			}

			warnRecordsUrlSyncMap.Store(id, urlList)
		}(warnRecordId, objectKeyList)
	}

	wg.Wait()

	warnRecordsUrlMap := make(map[int][]string)
	warnRecordsUrlSyncMap.Range(func(key, value interface{}) bool {
		warnRecordsUrlMap[key.(int)] = value.([]string)
		return true
	})

	respList := funk.Map(warnRecords, func(record model.JobWarnRecord) dto.JobWarnRecordResp {
		var position dto.JobWarnRecordPosition
		if e := json.Unmarshal([]byte(record.Position), &position); e != nil {
			s.logger.Errorf("failed to decode job warn record position, id: %d, err: %v", record.ID, e)
		}

		var warnType []string
		if e := json.Unmarshal([]byte(record.WarnType), &warnType); e != nil {
			s.logger.Errorf("failed to decode job warn record type, id: %d, err: %v", record.ID, e)
		}

		return dto.JobWarnRecordResp{
			ID:              record.ID,
			CompanyID:       record.CompanyID,
			JobRecordID:     record.JobRecordID,
			JobRecordName:   record.JobRecordName,
			WarnDesc:        record.WarnDesc,
			PicList:         warnRecordsUrlMap[record.ID],
			Position:        position,
			Level:           record.Level,
			WarnType:        warnType,
			Status:          record.Status,
			WarnAt:          record.WarnAt,
			HandleAt:        record.HandleAt,
			CreatedAt:       record.CreatedAt,
			UpdatedAt:       record.UpdatedAt,
			DroneSN:         record.DroneSN,
			JobWarnID:       record.JobWarnID,
			AlgorithmSource: record.AlgorithmSource,
		}
	}).([]dto.JobWarnRecordResp)

	return respList, nil
}

func (s *JobWarnRecordService) GetJobWarnRecordById(id int) (*model.JobWarnRecord, error) {
	var jobWarnRecord *model.JobWarnRecord
	err := s.db.Model(&model.JobWarnRecord{}).Where("id = ?", id).Find(&jobWarnRecord).Error
	if err != nil {
		return nil, err
	}

	return jobWarnRecord, nil
}

func (s *JobWarnRecordService) getWarnRecordByJobRecordId(jobRecordId int) ([]model.JobWarnRecord, error) {
	warnRecords := make([]model.JobWarnRecord, 0)
	err := s.db.Model(&model.JobWarnRecord{}).Where("job_record_id = ?", jobRecordId).Find(&warnRecords).Error
	if err != nil {
		return nil, err
	}

	return warnRecords, nil
}

func (s *JobWarnRecordService) convertWarnRecordToDto(warnRecords []model.JobWarnRecord) ([]dto.JobWarnRecordDto, error) {
	warnRecordDtoList := make([]dto.JobWarnRecordDto, 0)

	for _, record := range warnRecords {
		var picObjectKeyList []string
		if e := json.Unmarshal([]byte(record.PicList), &picObjectKeyList); e != nil {
			s.logger.Errorf("failed to decode job warn record pic list, id: %d, err: %v", record.ID, e)
			return nil, e
		}

		var position dto.JobWarnRecordPosition
		if e := json.Unmarshal([]byte(record.Position), &position); e != nil {
			s.logger.Errorf("failed to decode job warn record position, id: %d, err: %v", record.ID, e)
			return nil, e
		}

		var warnType []string
		if e := json.Unmarshal([]byte(record.WarnType), &warnType); e != nil {
			s.logger.Errorf("failed to decode job warn record type, id: %d, err: %v", record.ID, e)
			return nil, e
		}

		warnRecordDtoList = append(warnRecordDtoList, dto.JobWarnRecordDto{
			//ID:               record.ID,
			CompanyID:        record.CompanyID,
			JobRecordID:      record.JobRecordID,
			JobRecordName:    record.JobRecordName,
			WarnDesc:         record.WarnDesc,
			PicObjectKeyList: picObjectKeyList,
			Position:         position,
			Level:            record.Level,
			WarnType:         warnType,
			Status:           record.Status,
			WarnAt:           record.WarnAt,
			DroneSN:          record.DroneSN,
			JobWarnID:        record.JobWarnID,
		})
	}

	return warnRecordDtoList, nil
}

func (s *JobWarnRecordService) Update(params *dto.JobWarnRecordUpdateReq) error {
	columns := map[string]interface{}{
		"status":    params.Status,
		"handle_at": dbx.TimeStamp{Time: time.Now()},
	}

	err := s.db.Model(model.JobWarnRecord{}).Where("id = ?", params.ID).Updates(columns).Error
	if err != nil {
		return err
	}

	return nil
}

func (s *JobWarnRecordService) Overview(cid int) (interface{}, error) {
	var stats dto.WarnStatistics

	err := s.db.Model(&model.JobWarnRecord{}).Where("company_id = ?", cid).Count(&stats.TotalCount).Error
	if err != nil {
		return nil, err
	}

	err = s.db.Model(&model.JobWarnRecord{}).Where("company_id = ?", cid).Where("status = ?", enum.Processed).Count(&stats.ProcessedCount).Error
	if err != nil {
		return nil, err
	}

	err = s.db.Model(&model.JobWarnRecord{}).Where("company_id = ?", cid).Where("status = ?", enum.NotProcessed).Count(&stats.UnProcessCount).Error
	if err != nil {
		return nil, err
	}

	err = s.db.Model(&model.JobWarnRecord{}).Where("company_id = ?", cid).Where("status = ?", enum.Ignored).Count(&stats.IgnoredCount).Error
	if err != nil {
		return nil, err
	}

	err = s.db.Model(&model.JobWarnRecord{}).
		Select("COALESCE(ROUND(AVG(UNIX_TIMESTAMP(handle_at) - UNIX_TIMESTAMP(created_at))/3600, 2), 0)").
		Where("company_id = ?", cid).
		Where("status = ?", enum.Processed).
		Scan(&stats.AvgProcessHours).
		Error
	if err != nil {
		return nil, err
	}

	records := make([]model.JobWarnRecord, 0)
	err = s.db.Model(&model.JobWarnRecord{}).Where("company_id = ?", cid).Find(&records).Error
	if err != nil {
		return nil, err
	}

	typeCountMap := make(map[string]int)

	for _, record := range records {
		var warnTypes []string
		if err = json.Unmarshal([]byte(record.WarnType), &warnTypes); err != nil {
			return nil, err
		}

		for _, warnType := range warnTypes {
			typeCountMap[warnType]++
		}
	}

	var typeCounts []dto.TypeCount
	for warnType, count := range typeCountMap {
		var warnTypeStr string

		warnTypeEnum := enum.FindJobWarnTypeEnum(warnType)
		if warnTypeEnum == enum.JobWarnTypeEnumUnknown {
			warnTypeStr = warnType
		} else {
			warnTypeStr = warnTypeEnum.String()
		}

		typeCounts = append(typeCounts, dto.TypeCount{WarnType: warnTypeStr, Count: count})
	}

	return dto.JobWarnRecordOverviewResp{
		WarnStatistics: stats,
		TypeCount:      typeCounts,
	}, nil
}
