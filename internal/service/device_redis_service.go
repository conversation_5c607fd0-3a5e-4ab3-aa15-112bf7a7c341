package service

import (
	"encoding/json"
	"fmt"
	"strconv"
	device_request "wukong-api/cloud_sdk/cloud_api/device/request"
	"wukong-api/internal/dto"
	"wukong-api/internal/model"

	"github.com/go-redis/redis"

	"sync"
	"time"
	"wukong-api/internal/fileds"
	"wukong-api/internal/repo"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	deviceRedisServiceOnce sync.Once
	deviceRedisService     *DeviceRedisService
)

type DeviceRedisService struct {
	db     *gorm.DB
	logger *zap.SugaredLogger
	redis  *redis.Client
}

func NewDeviceRedisService() *DeviceRedisService {
	deviceRedisServiceOnce.Do(func() {
		deviceRedisService = &DeviceRedisService{
			db:     repo.GetDatabase(),
			logger: repo.GetLogger(),
			redis:  repo.GetRedis(0),
		}
	})
	return deviceRedisService
}

func (s *DeviceRedisService) CheckDeviceOnline(sn string) (bool, error) {
	key := fmt.Sprintf(fileds.DEVICE_ONLINE_PREFIX, sn)
	res, err := s.redis.Exists(key).Result()
	if err != nil {
		return false, err
	}

	return res > 0, nil
}

func (s *DeviceRedisService) getDeviceOnline(sn string) (*dto.DeviceDTO, error) {
	key := fmt.Sprintf(fileds.DEVICE_ONLINE_PREFIX, sn)
	res, err := s.redis.Get(key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		} else {
			return nil, err
		}
	}

	var data dto.DeviceDTO
	err = json.Unmarshal([]byte(res), &data)
	if err != nil {
		return nil, err
	}

	return &data, nil
}

func (s *DeviceRedisService) setDeviceOnline(device dto.DeviceDTO) error {
	encodedData, err := json.Marshal(device)
	if err != nil {
		return err
	}

	key := fmt.Sprintf(fileds.DEVICE_ONLINE_PREFIX, device.DeviceSN)
	err = s.redis.Set(key, encodedData, time.Duration(fileds.DEVICE_ALIVE_SECOND)*time.Second).Err()
	if err != nil {
		return err
	}

	return nil
}

func (s *DeviceRedisService) setDeviceOnlineWithModel(device model.Device) error {
	deviceDto := dto.ConvertDeviceToDeviceDTO(device)

	deviceOnline, _ := s.getDeviceOnline(device.DeviceSN)
	if deviceOnline != nil {
		deviceDto.PayloadsList = deviceOnline.PayloadsList
		deviceDto.DeviceIndex = device.DeviceIndex
	}

	return s.setDeviceOnline(*deviceDto)
}

func (s *DeviceRedisService) delDeviceOnline(sn string) error {
	key := fmt.Sprintf(fileds.DEVICE_ONLINE_PREFIX, sn)
	if err := s.redis.Del(key).Err(); err != nil {
		return err
	}
	return nil
}

func (s *DeviceRedisService) setDockOsd(sn string, data device_request.OsdDock) error {
	encodedData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	key := fmt.Sprintf(fileds.OSD_PREFIX, sn)
	err = s.redis.Set(key, encodedData, 0).Err()
	if err != nil {
		return err
	}

	return nil
}

func (s *DeviceRedisService) getDockOsd(sn string) (*device_request.OsdDock, error) {
	key := fmt.Sprintf(fileds.OSD_PREFIX, sn)
	res, err := s.redis.Get(key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		} else {
			return nil, err
		}
	}

	var data device_request.OsdDock
	err = json.Unmarshal([]byte(res), &data)
	if err != nil {
		return nil, err
	}

	return &data, nil
}

func (s *DeviceRedisService) setDroneOsd(sn string, data interface{}) error {
	encodedData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	key := fmt.Sprintf(fileds.OSD_PREFIX, sn)
	err = s.redis.Set(key, encodedData, 0).Err()
	if err != nil {
		return err
	}

	return nil
}

func (s *DeviceRedisService) getDockDroneOsd(sn string) (*device_request.OsdDockDrone, error) {
	key := fmt.Sprintf(fileds.OSD_PREFIX, sn)
	res, err := s.redis.Get(key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		} else {
			return nil, err
		}
	}

	var data device_request.OsdDockDrone
	err = json.Unmarshal([]byte(res), &data)
	if err != nil {
		return nil, err
	}

	return &data, nil
}

func (s *DeviceRedisService) getRcDroneOsd(sn string) (*device_request.OsdRCDrone, error) {
	key := fmt.Sprintf(fileds.OSD_PREFIX, sn)
	res, err := s.redis.Get(key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		} else {
			return nil, err
		}
	}

	var data device_request.OsdRCDrone
	err = json.Unmarshal([]byte(res), &data)
	if err != nil {
		return nil, err
	}

	return &data, nil
}

func (s *DeviceRedisService) delDeviceOsd(sn string) error {
	key := fmt.Sprintf(fileds.OSD_PREFIX, sn)
	if err := s.redis.Del(key).Err(); err != nil {
		return err
	}
	return nil
}

func (s *DeviceRedisService) DelDeviceLiveStreaming(sn string) error {
	key := fmt.Sprintf(fileds.LIVE_STREAMING, sn)
	if err := s.redis.Del(key).Err(); err != nil {
		return err
	}
	//s.logger.Infof("DeviceRedisService, DelDeviceLiveStreaming success, sn:%s", sn)
	return nil
}

func (s *DeviceRedisService) setRcOsd(sn string, data device_request.OsdRC) error {
	encodedData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	key := fmt.Sprintf(fileds.OSD_PREFIX, sn)
	err = s.redis.Set(key, encodedData, 0).Err()
	if err != nil {
		return err
	}

	return nil
}

func (s *DeviceRedisService) getRcOsd(sn string) (*device_request.OsdRC, error) {
	key := fmt.Sprintf(fileds.OSD_PREFIX, sn)
	res, err := s.redis.Get(key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		} else {
			return nil, err
		}
	}

	var data device_request.OsdRC
	err = json.Unmarshal([]byte(res), &data)
	if err != nil {
		return nil, err
	}

	return &data, nil
}

// CheckIsOccupiedPermissions 检查是否被占用
func CheckIsOccupiedPermissions() bool {
	res, _ := repo.GetRedis(0).Get(fileds.IsOccupiedPermissions).Result()
	return res == "1"
}

// CheckIsDoNoRealTime 检查是否开启了非实时处理
func CheckIsDoNoRealTime() bool {
	res, _ := repo.GetRedis(0).Get(fileds.IsDoNoRealTime).Result()
	return res == "1"
}

// GetNoRealTimeCompany 获取需要非实时处理的公司
func GetNoRealTimeCompany() int {
	res, _ := repo.GetRedis(0).Get(fileds.NoRealTimeCompany).Result()
	if res == "" {
		return 0
	}

	cid, _ := strconv.Atoi(res)
	return cid
}
