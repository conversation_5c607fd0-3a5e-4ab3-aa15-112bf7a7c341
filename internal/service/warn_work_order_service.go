package service

import (
	"encoding/json"
	"sync"
	"time"
	"wukong-api/internal/dto"
	"wukong-api/internal/model"
	"wukong-api/internal/repo"

	"github.com/thoas/go-funk"

	"github.com/qxsugar/pkg/apix"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	warnWorkOrderServiceOnce sync.Once
	warnWorkOrderService     *WarnWorkOrderService
)

type WarnWorkOrderService struct {
	db     *gorm.DB
	logger *zap.SugaredLogger
}

func NewWarnWorkOrderService() *WarnWorkOrderService {
	warnWorkOrderServiceOnce.Do(func() {
		warnWorkOrderService = &WarnWorkOrderService{
			db:     repo.GetDatabase(),
			logger: repo.GetLogger(),
		}
	})
	return warnWorkOrderService
}

func (s *WarnWorkOrderService) ListWorkOrder(params *dto.ListWorkOrderReq, cid int) (*apix.PageBody, error) {
	var count int64

	// 告警
	warns := make([]model.JobWarn, 0)

	op := s.db.Model(&model.JobWarn{})
	op = op.Order("id desc").Where("company_id = ?", cid).Where("user_id IS NOT NULL AND user_id != 0")

	if params.StartTime != 0 {
		op.Where("handle_at >= ?", time.Unix(params.StartTime, 0))
	}
	if params.EndTime != 0 {
		op.Where("handle_at <= ?", time.Unix(params.EndTime, 0))
	}
	if params.Level != 0 {
		op.Where("level = ?", params.Level)
	}
	if params.Status != 0 {
		op.Where("status = ?", params.Status)
	}
	if params.JobWarnID != 0 {
		op.Where("id = ?", params.JobWarnID)
	}

	op = op.Count(&count).Offset(params.Offset).Limit(params.Limit)
	err := op.Scan(&warns).Error
	if err != nil {
		return nil, err
	}

	var warnIds, userIds []int
	for _, warn := range warns {
		warnIds = append(warnIds, warn.ID)
		userIds = append(userIds, warn.UserID)
	}

	// 告警记录
	var warnRecords []model.JobWarnRecord

	warnRecordOp := s.db.Model(&model.JobWarnRecord{}).Where("job_warn_id IN ?", warnIds)
	err = warnRecordOp.Find(&warnRecords).Error
	if err != nil {
		return nil, err
	}

	groupedWarnRecords := make(map[int][]model.JobWarnRecord)
	for _, record := range warnRecords {
		groupedWarnRecords[record.JobWarnID] = append(groupedWarnRecords[record.JobWarnID], record)
	}

	// 人员
	var users []model.CompanyUser

	userOp := s.db.Model(&model.CompanyUser{}).Select("id, nickname").Where("id IN ?", userIds)
	err = userOp.Find(&users).Error
	if err != nil {
		return nil, err
	}

	respList := funk.Map(warns, func(warn model.JobWarn) dto.WorkOrderResp {
		var warnType []string
		err = json.Unmarshal([]byte(warn.WarnType), &warnType)
		if err != nil {
			s.logger.Errorf("failed to unmarshal warn type, warn id: %d, err: %v", warn.ID, err)
		}

		var userNickName string
		for _, user := range users {
			if user.ID == warn.UserID {
				userNickName = user.Nickname
				break
			}
		}

		var positionList []dto.JobWarnPosition
		warnRecordList := groupedWarnRecords[warn.ID]

		var warnRecordHandleCount int
		for _, r := range warnRecordList {
			if r.Status == 1 {
				warnRecordHandleCount++
			}

			var position dto.JobWarnRecordPosition
			if e := json.Unmarshal([]byte(r.Position), &position); e != nil {
				s.logger.Errorf("failed to decode job warn record position, id: %d, err: %v", r.ID, e)
			}

			positionList = append(positionList, dto.JobWarnPosition{
				WarnRecordID: r.ID,
				WarnDesc:     r.WarnDesc,
				Bd09ll:       position.Bd09ll,
				Standard:     position.Standard,
			})
		}

		return dto.WorkOrderResp{
			JobWarnID:             warn.ID,
			CompanyID:             warn.CompanyID,
			JobRecordID:           warn.JobRecordID,
			JobRecordName:         warn.JobRecordName,
			WarnType:              warnType,
			Level:                 warn.Level,
			Status:                warn.Status,
			UserID:                warn.UserID,
			UserNickName:          userNickName,
			HandleAt:              warn.HandleAt,
			WarnRecordCount:       len(warnRecordList),
			WarnRecordHandleCount: warnRecordHandleCount,
			WarnAt:                warn.WarnAt,
			PositionList:          positionList,
		}
	}).([]dto.WorkOrderResp)

	return &apix.PageBody{
		Offset: params.Offset,
		Limit:  params.Limit,
		Total:  count,
		List:   respList,
	}, nil
}

func (s *WarnWorkOrderService) Detail(params *dto.WorkOrderDetailReq) ([]dto.WorkOrderHandleDetailResp, error) {
	var warnRecords []model.JobWarnRecordWithHandle

	op := s.db.Model(&model.JobWarnRecord{}).
		Select("job_warn_record.*, job_warn_record_handle.handle_images, job_warn_record_handle.handle_result").
		Joins("LEFT JOIN job_warn_record_handle ON job_warn_record.id = job_warn_record_handle.job_warn_record_id").
		Where("job_warn_record.job_warn_id = ?", params.JobWarnID).
		Order("job_warn_record.status asc")

	err := op.Scan(&warnRecords).Error
	if err != nil {
		return nil, err
	}

	ossSvc := NewOssService()

	respList := funk.Map(warnRecords, func(warn model.JobWarnRecordWithHandle) dto.WorkOrderHandleDetailResp {
		var picList []string
		err = json.Unmarshal([]byte(warn.PicList), &picList)
		if err != nil {
			s.logger.Errorf("failed to unmarshal pic list, warn id: %d, err: %v", warn.ID, err)
		}

		picUrlList := make([]string, 0)
		for _, picKey := range picList {
			var url string
			url, err = ossSvc.GetObjectSignURL(picKey, int64(60*60*24))
			if err != nil {
				s.logger.Errorf("failed to get object sign url, pic key: %s, err: %v", picKey, err)
				continue
			}
			picUrlList = append(picUrlList, url)
		}

		var handleFileList []string
		if warn.HandleImages != "" {
			err = json.Unmarshal([]byte(warn.HandleImages), &handleFileList)
			if err != nil {
				s.logger.Errorf("failed to unmarshal HandleImages, warn id: %d, err: %v", warn.ID, err)
			}
		}

		handleFileUrlList := make([]string, 0)
		for _, fileKey := range handleFileList {
			var url string
			url, err = ossSvc.GetObjectSignURL(fileKey, int64(60*60*24))
			if err != nil {
				s.logger.Errorf("failed to get object sign url, pic key: %s, err: %v", fileKey, err)
				continue
			}
			handleFileUrlList = append(handleFileUrlList, url)
		}

		var position dto.JobWarnRecordPosition
		if warn.Position != "" {
			if e := json.Unmarshal([]byte(warn.Position), &position); e != nil {
				s.logger.Errorf("failed to decode job warn record position, id: %d, err: %v", warn.ID, e)
			}
		}

		return dto.WorkOrderHandleDetailResp{
			JobWarnRecordID: warn.JobRecordID,
			Level:           warn.Level,
			WarnDesc:        warn.WarnDesc,
			PicList:         picUrlList,
			Position:        position,
			WarnAt:          warn.WarnAt,
			Status:          warn.Status,
			HandleAt:        warn.HandleAt,
			HandleResult:    warn.HandleResult,
			UrlList:         handleFileUrlList,
		}
	}).([]dto.WorkOrderHandleDetailResp)

	return respList, nil
}

func (s *WarnWorkOrderService) Delete(params *dto.WorkOrderDeleteReq) error {
	return s.db.Model(&model.JobWarn{}).Where("id = ?", params.JobWarnID).Update("user_id", nil).Error
}
