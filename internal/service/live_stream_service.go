package service

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"
	device_cloud_enum "wukong-api/cloud_sdk/cloud_api/device/cloud_enum"
	device_request "wukong-api/cloud_sdk/cloud_api/device/request"
	"wukong-api/cloud_sdk/cloud_api/live_stream/cloud_enum"
	"wukong-api/cloud_sdk/cloud_api/live_stream/request"
	wayline_cloud_enum "wukong-api/cloud_sdk/cloud_api/wayline/cloud_enum"
	"wukong-api/cloud_sdk/mqtt"
	"wukong-api/internal/cloudinfra"
	"wukong-api/internal/dto"
	"wukong-api/internal/enum"
	"wukong-api/internal/fileds"
	"wukong-api/internal/model"
	"wukong-api/internal/repo"

	"github.com/qxsugar/pkg/dbx"

	"github.com/go-redis/redis"
	"github.com/thoas/go-funk"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
)

var (
	liveStreamServiceOnce sync.Once
	liveStreamService     *LiveStreamService
)

type LiveStreamService struct {
	db                     *gorm.DB
	logger                 *zap.SugaredLogger
	redis                  *redis.Client
	capacityCameraService  *CapacityCameraService
	liveStreamRedisService *LiveStreamRedisService
}

func NewLiveStreamService() *LiveStreamService {
	liveStreamServiceOnce.Do(func() {
		liveStreamService = &LiveStreamService{
			db:                     repo.GetDatabase(),
			logger:                 repo.GetLogger(),
			redis:                  repo.GetRedis(0),
			capacityCameraService:  NewCapacityCameraService(),
			liveStreamRedisService: NewLiveStreamRedisService(),
		}
	})
	return liveStreamService
}

func (s *LiveStreamService) StartLiveStreamWithRcCapacityUp(deviceSnList []string) error {
	var droneList []*model.Drone
	err := s.db.Model(&model.Drone{}).Not("manual_job_id", "").Find(&droneList).Error
	if err != nil {
		return err
	}

	doDroneList := funk.Filter(droneList, func(drone *model.Drone) bool {
		return funk.Contains(deviceSnList, drone.VehicleId)
	}).([]*model.Drone)

	for _, doDrone := range doDroneList {
		if err = s.startDroneLiveStream(doDrone.VehicleId, doDrone.ManualJobId, enum.JobTypeEnum_Manual); err != nil {
			s.logger.Errorf("StartLiveStreamWithRcCapacityUp: startDroneLiveStream failed, droneSn: %v, err: %v", doDrone.VehicleId, err)
			continue
		}
	}

	return nil
}

func (s *LiveStreamService) StartLiveStreamWithDockCapacityUp(deviceSnList []string) error {
	var droneList []*model.Drone
	err := s.db.Model(&model.Drone{}).Not("wayline_job_id", "").Find(&droneList).Error
	if err != nil {
		return err
	}

	doDroneList := funk.Filter(droneList, func(drone *model.Drone) bool {
		return funk.Contains(deviceSnList, drone.VehicleId)
	}).([]*model.Drone)

	for _, doDrone := range doDroneList {
		if err = s.startDroneLiveStream(doDrone.VehicleId, doDrone.WaylineJobId, enum.JobTypeEnum_Wayline); err != nil {
			s.logger.Errorf("StartLiveStreamWithDockCapacityUp: startDroneLiveStream failed, droneSn: %v, err: %v", doDrone.VehicleId, err)
			continue
		}
	}

	var dockList []*model.Device
	err = s.db.Model(model.Device{}).Where("domain = ?", device_cloud_enum.Dock.Value()).Find(&dockList).Error
	if err != nil {
		return err
	}

	doDockList := funk.Filter(dockList, func(dock *model.Device) bool {
		return funk.Contains(deviceSnList, dock.DeviceSN)
	}).([]*model.Device)

	for _, doDock := range doDockList {
		//s.logger.Infof("StartLiveStreamWithDockCapacityUp: StartDockLiveStream, dockSn: %v", doDock.DeviceSN)
		if err = s.StartDockLiveStream(doDock); err != nil {
			s.logger.Errorf("StartLiveStreamWithDockCapacityUp: StartDockLiveStream failed, dockSn: %v, err: %v", doDock.DeviceSN, err)
			continue
		}
	}

	return nil
}

func (s *LiveStreamService) StopLiveStreamWithDrone(droneSn string) error {
	data, err := s.liveStreamRedisService.getLiveStreaming(droneSn)
	if err != nil {
		return err
	}

	if data == nil {
		return nil
	}

	if data.LiveStatus == nil || len(data.LiveStatus) == 0 {
		_ = NewDeviceRedisService().DelDeviceLiveStreaming(droneSn)
		return nil
	}

	var g errgroup.Group

	for _, liveStatus := range data.LiveStatus {
		capacity := liveStatus // 避免闭包问题

		g.Go(func() error {
			videoId := capacity.VideoId

			req := request.LiveStopPushRequest{
				VideoId: videoId,
			}

			var reply *mqtt.CommonTopicResponse[mqtt.MqttReply]
			reply, err = cloudinfra.LiveStopPush(data.GatewaySn, req)
			if err != nil {
				return err
			}

			if *reply.Data.Result != 0 && *reply.Data.Result != 513011 {
				return fmt.Errorf("stop drone live stream failed, videoId: %s, code: %v", videoId, *reply.Data.Result)
			}

			return nil
		})
	}

	if err = g.Wait(); err != nil {
		return err
	}

	_ = NewDeviceRedisService().DelDeviceLiveStreaming(droneSn)

	return nil
}

func (s *LiveStreamService) GetDeviceLiveStreamURL(droneSn string) ([]dto.LiveStatusDTO, error) {
	liveStreaming, err := s.liveStreamRedisService.getLiveStreaming(droneSn)
	if err != nil {
		return nil, err
	}

	if liveStreaming == nil {
		return nil, nil
	}

	return liveStreaming.LiveStatus, nil
}

func (s *LiveStreamService) startDroneLiveStream(droneSn, jobId string, jobType enum.JobTypeEnum) error {
	lockKey := fmt.Sprintf(fileds.ControlLiveStreamLock, droneSn)

	acquired, err := s.redis.SetNX(lockKey, "locked", fileds.ControlLiveStreamLockTimeout).Result()
	if err != nil {
		s.logger.Errorf("startDroneLiveStream: failed to lock. lockKey: %s, err: %v", lockKey, err)
		return err
	}
	if !acquired {
		s.logger.Debugf("startDroneLiveStream: lock:%s is exist", lockKey)
		return nil
	}

	defer func() {
		if err = s.redis.Del(lockKey).Err(); err != nil {
			s.logger.Errorf("startDroneLiveStream: failed to del lock key: %s, err: %v", lockKey, err)
			return
		}
	}()

	liveCapacityCamera, err := s.capacityCameraService.getCapacityCameraByDeviceSn(droneSn)
	if err != nil {
		return err
	}

	if liveCapacityCamera == nil || len(liveCapacityCamera) == 0 {
		return nil
	}

	key := fmt.Sprintf(fileds.LIVE_STREAMING, droneSn)

	var oldDroneLiveStreaming dto.DeviceLiveStreaming
	oldDroneLiveStreamingIsExist := false

	result, _ := s.redis.Get(key).Result()
	if result != "" {
		if err = json.Unmarshal([]byte(result), &oldDroneLiveStreaming); err != nil {
			s.logger.Errorf("decode old job video ids failed, jobId: %v, err: %v", jobId, err)
			return err
		}

		oldDroneLiveStreamingIsExist = true
	}

	deviceSvc := NewDeviceService()
	liveStreamServerSvc := NewLiveStreamServerService()

	liveStatusList := make([]dto.LiveStatusDTO, 0)

	var gateway *model.DeviceWithCompany
	if gateway, err = deviceSvc.getDeviceCompanyByChildSn(droneSn); err != nil {
		s.logger.Errorf("get device by child sn failed, childSn: %v, err: %v", droneSn, err)
		return err
	}

	gatewaySn := gateway.DeviceSN

	var drone *model.Device
	if drone, err = deviceSvc.GetDeviceBySn(droneSn); err != nil {
		return err
	}

	var liveStreamServer model.LiveStreamServer
	err = s.db.Where("id = ?", drone.LiveStreamServerID).First(&liveStreamServer).Error
	if err != nil {
		return err
	}

	// 是否有开启新的直播
	isNewStart := false
	// 是否新开启了云台相机直播
	isNewStartGimbal := false

	for _, camera := range liveCapacityCamera {
		cameraVideoId := camera.VideoId

		// 如果已经相机已经在直播中，则跳过
		if oldDroneLiveStreamingIsExist {
			flag := false
			for _, status := range oldDroneLiveStreaming.LiveStatus {
				if status.VideoId == cameraVideoId {
					liveStatusList = append(liveStatusList, status)
					flag = true
					break
				}
			}

			if flag {
				continue
			}
		}

		var stream string
		if jobId == "" {
			stream = fmt.Sprintf("%s_%s", droneSn, camera.CameraIndex)
		} else {
			stream = fmt.Sprintf("%s_%s_%s", droneSn, camera.CameraIndex, jobId)
		}

		var pushUrl string
		pushUrl, err = liveStreamServerSvc.genLiveStreamUrlWithRecord(liveStreamServer, stream, enum.LiveStreamType_Push)
		if err != nil {
			return err
		}

		s.logger.Debugf("startDroneLiveStream: drone live stream url: %s, droneSn: %s, jobId: %s.", pushUrl, droneSn, jobId)

		quality := cloud_enum.ULTRA_HD
		//quality := cloud_enum.STANDARD_DEFINITION
		//该公司的画质为高清
		hdCompanyIDs := []int{1, 2, 4, 5, 90, 91, 93}
		for _, companyID := range gateway.CompanyID {
			if funk.ContainsInt(hdCompanyIDs, companyID) {
				quality = cloud_enum.HIGH_DEFINITION
				break
			}
		}

		urlType := liveStreamServerSvc.genWebrtcLiveStreamUrlWithType(liveStreamServer)
		if _, err = s.StartLiveStream(urlType, gatewaySn, pushUrl, cameraVideoId, quality); err != nil {
			s.logger.Errorf("start drone live stream failed, droneSn: %v, videoId: %s, err: %v", droneSn, cameraVideoId, err)
			continue
		}

		if IsGimbalByPayloadModelKey(camera.CameraIndex) {
			isNewStartGimbal = true
		}

		var pullUrl string
		pullUrl, err = liveStreamServerSvc.genLiveStreamUrlWithRecord(liveStreamServer, stream, enum.LiveStreamType_Pull)
		if err != nil {
			return err
		}

		isNewStart = true
		liveStatusList = append(liveStatusList, dto.LiveStatusDTO{
			VideoQuality:          quality,
			LiveCapacityCameraDTO: *camera,
			StreamURL:             pullUrl,
		})

		// 演示逻辑: 切换大屏直播画面
		if strings.Contains(stream, "1581F5BLD232B00138U1_52-0-0") {
			go func() {
				time.Sleep(15 * time.Second)

				var newVisualization model.Visualization
				if err = s.db.Model(model.Visualization{}).Where("id = 1").First(&newVisualization).Error; err != nil {
					return
				}

				if newVisualization.StreamType != 1 && newVisualization.StreamType != 2 {
					res, _ := s.redis.Get(fileds.Visualization_LIVE_STREAMING).Result()

					var streamType int
					if res != "" {
						streamType, _ = strconv.Atoi(res)
					}
					if streamType == 0 {
						streamType = 1
					}

					update := map[string]interface{}{
						"stream_type": streamType,
						"refresh":     newVisualization.Refresh + 1,
					}
					err = s.db.Model(model.Visualization{}).Where("id = 1").Updates(update).Error
					if err != nil {
						return
					}
					s.logger.Debugf("visualization update, stream_type: %d, refresh: %d", newVisualization.StreamType, newVisualization.Refresh)
				}
			}()
		}
	}

	if isNewStartGimbal {
		// 记录开启直播的时间戳
		var jobRecord *model.InspectionJobRecord
		if jobType == enum.JobTypeEnum_Manual {
			if jobRecord, err = NewInspectionJobRecordService().getByManualJobId(jobId); err != nil {
				s.logger.Errorf("failed to get job record, manualJobId:%s", jobId)
				return err
			}
		} else if jobType == enum.JobTypeEnum_Wayline {
			if jobRecord, err = NewInspectionJobRecordService().getByWaylineJobId(jobId); err != nil {
				s.logger.Errorf("failed to get job record, waylineJobId:%s", jobId)
				return err
			}
		}

		if jobRecord != nil && jobRecord.LiveStartTime.IsZero() {
			_ = NewJobLogsService().SaveJobLogByGateway(jobType, jobId, gatewaySn, "无人机开始直播", false)

			now := time.Now()

			data := dto.LiveStreamingJob{
				JobID:         jobId,
				LiveStartTime: int(now.UnixMilli()),
				DroneSn:       droneSn,
			}

			err = s.liveStreamRedisService.setLiveStreamingJob(gatewaySn, data)
			if err != nil {
				s.logger.Errorf("failed to set live_streaming_job, jobId:%s", jobId)
			}

			err = s.db.Model(&model.InspectionJobRecord{}).Where("id = ?", jobRecord.ID).Update("live_start_time", dbx.TimeStamp{Time: now}).Error
			if err != nil {
				s.logger.Errorf("failed to set live_start_time, job_record_id:%d", jobRecord.ID)
				return err
			}
		}
	}

	if isNewStart {
		data := dto.DeviceLiveStreaming{
			GatewaySn:  gatewaySn,
			LiveStatus: liveStatusList,
		}

		if err = s.liveStreamRedisService.setLiveStreaming(droneSn, data); err != nil {
			s.logger.Errorf("set live streaming failed, err: %v", err)
			return err
		}
	}

	return nil
}

func (s *LiveStreamService) StartDockLiveStream(dock *model.Device) error {
	dockSn := dock.DeviceSN
	liveCapacityCamera, err := s.capacityCameraService.getCapacityCameraByDeviceSn(dockSn)
	if err != nil {
		return err
	}

	if liveCapacityCamera == nil || len(liveCapacityCamera) == 0 {
		//s.logger.Infof("StartDockLiveStream: no live capacity camera, dockSn: %s", dockSn)
		return nil
	}

	key := fmt.Sprintf(fileds.LIVE_STREAMING, dockSn)

	var oldDockLiveStreaming dto.DeviceLiveStreaming
	oldDockLiveStreamingIsExist := false

	result, _ := s.redis.Get(key).Result()
	if result != "" {
		if err = json.Unmarshal([]byte(result), &oldDockLiveStreaming); err != nil {
			s.logger.Errorf("decode old job video ids failed, err: %v", err)
			return err
		}

		oldDockLiveStreamingIsExist = true
	}

	liveStreamServerSvc := NewLiveStreamServerService()

	liveStatusList := make([]dto.LiveStatusDTO, 0)

	var liveStreamServer model.LiveStreamServer
	err = s.db.Where("id = ?", dock.LiveStreamServerID).First(&liveStreamServer).Error
	if err != nil {
		return err
	}

	// 是否有开启新的直播
	isNewStart := false

	for _, camera := range liveCapacityCamera {
		cameraVideoId := camera.VideoId

		// 如果已经相机已经在直播中，则跳过
		if oldDockLiveStreamingIsExist {
			flag := false
			for _, status := range oldDockLiveStreaming.LiveStatus {
				if status.VideoId == cameraVideoId {
					liveStatusList = append(liveStatusList, status)
					flag = true
					break
				}
			}

			if flag {
				//s.logger.Infof("StartDockLiveStream: camera already in live streaming, dockSn: %s, cameraVideoId: %s", dockSn, cameraVideoId)
				continue
			}
		}

		stream := fmt.Sprintf("%s_%s", dockSn, camera.CameraIndex)

		var pushUrl string
		pushUrl, err = liveStreamServerSvc.genLiveStreamUrlWithRecord(liveStreamServer, stream, enum.LiveStreamType_Push)
		if err != nil {
			return err
		}

		urlType := liveStreamServerSvc.genWebrtcLiveStreamUrlWithType(liveStreamServer)
		if _, err = s.StartLiveStream(urlType, dockSn, pushUrl, cameraVideoId, cloud_enum.SMOOTH); err != nil {
			s.logger.Errorf("start dock live stream failed, droneSn: %v, videoId: %s, err: %v", dockSn, cameraVideoId, err)
			continue
		}

		//s.logger.Infof("StartDockLiveStream: start live stream success, dockSn: %s, cameraVideoId: %s", dockSn, cameraVideoId)

		var pullUrl string
		pullUrl, err = liveStreamServerSvc.genLiveStreamUrlWithRecord(liveStreamServer, stream, enum.LiveStreamType_Pull)
		if err != nil {
			return err
		}

		isNewStart = true
		liveStatusList = append(liveStatusList, dto.LiveStatusDTO{
			VideoQuality:          cloud_enum.SMOOTH,
			LiveCapacityCameraDTO: *camera,
			StreamURL:             pullUrl,
		})
	}

	if isNewStart {
		data := dto.DeviceLiveStreaming{
			GatewaySn:  dockSn,
			LiveStatus: liveStatusList,
		}

		if err = s.liveStreamRedisService.setLiveStreaming(dockSn, data); err != nil {
			s.logger.Errorf("set live streaming failed, err: %v", err)
			return err
		}

		//s.logger.Infof("StartDockLiveStream: set live streaming success, dockSn: %s", dockSn)
	}

	return nil
}

func (s *LiveStreamService) StartLiveStream(urlType cloud_enum.UrlTypeEnum, gatewaySn, url, videoId string, videoQuality cloud_enum.VideoQualityEnum) (*int, error) {
	req := request.LiveStartPushRequest{
		UrlType:      urlType,
		Url:          url,
		VideoId:      videoId,
		VideoQuality: videoQuality,
	}

	reply, err := cloudinfra.LiveStartPush(gatewaySn, req)
	if err != nil {
		return nil, err
	}
	// 513003 相机已经在直播中，请勿重复开启直播
	if *reply.Data.Result != 0 && *reply.Data.Result != 513003 {
		return reply.Data.Result, fmt.Errorf("start live stream failed, code: %v", *reply.Data.Result)
	}

	s.logger.Infof("StartLiveStream: req>>>>>>>>>>>>>>>>>>>>>: %+v", *reply.Data.Result)
	s.logger.Infof("StartLiveStream: url>>>>>>>>>>>>>>>>>>>>>: %v", url)
	s.logger.Infof("StartLiveStream: urlType>>>>>>>>>>>>>>>>>>>>>: %v", urlType)

	return reply.Data.Result, nil
}

func (s *LiveStreamService) dockLivestreamAbilityUpdate(liveCapacity *device_request.LiveCapacity, gatewaySn string, timestamp int) error {
	return s.saveLiveCapacity(liveCapacity.DeviceList, gatewaySn, timestamp)
}

func (s *LiveStreamService) rcLivestreamAbilityUpdate(liveCapacity *device_request.LiveCapacity, gatewaySn string, timestamp int) error {
	return s.saveLiveCapacity(liveCapacity.DeviceList, gatewaySn, timestamp)
}

func (s *LiveStreamService) saveLiveCapacity(liveCapacityDevices []device_request.LiveCapacityDevice, gatewaySn string, timestamp int) error {
	for _, liveCapacityDevice := range liveCapacityDevices {
		deviceSn := liveCapacityDevice.Sn

		// 对比时间戳
		capacityTimestamp, _ := s.liveStreamRedisService.getLiveCapacityTimestamp(deviceSn)
		if capacityTimestamp > timestamp {
			s.logger.Debugf("saveLiveCapacity: capacityTimestamp:%d > timestamp:%d, continue", capacityTimestamp, timestamp)
			continue
		}

		if err := s.capacityCameraService.saveCapacityCameraList(liveCapacityDevice.CameraList, deviceSn); err != nil {
			s.logger.Errorf("save capacity failed, deviceSn: %s, err: %v", deviceSn, err)
			continue
		}

		if err := s.liveStreamRedisService.saveLiveCapacityTimestamp(deviceSn, timestamp); err != nil {
			s.logger.Errorf("save capacity timestamp failed, deviceSn: %s, err: %v", deviceSn, err)
		}

		if err := s.filterLiveStreamingByCapacity(deviceSn, gatewaySn); err != nil {
			s.logger.Errorf("update live streaming failed, deviceSn: %s, err: %v", deviceSn, err)
			continue
		}
	}

	return nil
}

// filterLiveStreamingByCapacity 过滤没有对应capacity的live_streaming
func (s *LiveStreamService) filterLiveStreamingByCapacity(deviceSn, gatewaySn string) error {
	liveStreaming, err := s.liveStreamRedisService.getLiveStreaming(deviceSn)
	if err != nil {
		s.logger.Errorf("get live streaming failed, deviceSn: %s, err: %v", deviceSn, err)
		return err
	}

	if liveStreaming == nil {
		return nil
	}

	liveCapacityList, err := s.capacityCameraService.getCapacityCameraByDeviceSn(deviceSn)
	if err != nil {
		s.logger.Errorf("get live capacity failed, deviceSn: %s, err: %v", deviceSn, err)
		return err
	}

	liveStatusList := make([]dto.LiveStatusDTO, 0)

	for _, status := range liveStreaming.LiveStatus {
		for _, capacity := range liveCapacityList {
			if status.VideoId == capacity.VideoId {
				liveStatusList = append(liveStatusList, status)
				break
			}
		}
	}

	if len(liveStatusList) != len(liveStreaming.LiveStatus) {
		data := dto.DeviceLiveStreaming{
			GatewaySn:  gatewaySn,
			LiveStatus: liveStatusList,
		}

		if err = s.liveStreamRedisService.setLiveStreaming(deviceSn, data); err != nil {
			s.logger.Errorf("set live streaming failed, err: %v", err)
			return err
		}
	}

	return nil
}

func (s *LiveStreamService) dockLivestreamStatusUpdate(liveStatus []device_request.LiveStatus) error {
	for _, status := range liveStatus {
		if status.VideoId == nil {
			continue
		}

		split := strings.Split(*status.VideoId, "/")
		if len(split) == 0 {
			continue
		}

		deviceSn := split[0]

		liveStreaming, err := s.liveStreamRedisService.getLiveStreaming(deviceSn)
		if err != nil {
			s.logger.Errorf("get live streaming failed, err: %v", err)
			continue
		}

		if liveStreaming == nil {
			continue
		}

		flag := false

		newLiveStreaming := liveStreaming

		for i, oldStatus := range liveStreaming.LiveStatus {
			if oldStatus.VideoId == *status.VideoId {
				if status.VideoQuality != nil && oldStatus.VideoQuality != *status.VideoQuality {
					newLiveStreaming.LiveStatus[i].VideoQuality = *status.VideoQuality
					flag = true
				}
				if status.VideoType != nil && oldStatus.VideoType != *status.VideoType {
					newLiveStreaming.LiveStatus[i].VideoType = *status.VideoType
					flag = true
				}

				break
			}
		}

		if flag {
			if err = s.liveStreamRedisService.setLiveStreaming(deviceSn, *newLiveStreaming); err != nil {
				s.logger.Errorf("set live streaming failed, err: %v", err)
				return err
			}
		}
	}

	return nil
}

// rcLivestreamStatusUpdate rc网关上报直播状态
// Matrice 4TD 只上报了status，并无其他信息；{"live_status":[{"status":1,"video_id":"","video_quality":0}]}
func (s *LiveStreamService) rcLivestreamStatusUpdate(liveStatus []device_request.LiveStatus, gatewaySn string) error {
	if len(liveStatus) == 0 {
		return nil
	}

	gateway, err := NewDeviceRedisService().getDeviceOnline(gatewaySn)
	if err != nil {
		s.logger.Errorf("get gateway failed, gatewaySn: %s, err: %v", gatewaySn, err)
		return err
	}

	if gateway == nil {
		return nil
	}

	deviceSn := gateway.ChildSN

	liveStreaming, err := s.liveStreamRedisService.getLiveStreaming(deviceSn)
	if err != nil {
		s.logger.Errorf("get live streaming failed, err: %v", err)
		return err
	}

	if liveStreaming == nil {
		return nil
	}

	newStatus := liveStatus[0]

	// 如果状态为0，直接删除
	if newStatus.Status != nil && *newStatus.Status == 0 {
		err = s.liveStreamRedisService.delLiveStreaming(deviceSn)
		if err != nil {
			s.logger.Errorf("del live streaming failed, deviceSn: %s, err: %v", deviceSn, err)
			return err
		}
	}

	return nil
}

func (s *LiveStreamService) DelLivestreamWithVideoId(deviceSn, videoId string) error {
	lockKey := fmt.Sprintf(fileds.ControlLiveStreamLock, deviceSn)

	acquired, err := s.redis.SetNX(lockKey, "locked", fileds.ControlLiveStreamLockTimeout).Result()
	if err != nil {
		s.logger.Errorf("delLivestreamWithVideoId: failed to lock. lockKey: %s, err: %v", lockKey, err)
		return err
	}
	if !acquired {
		s.logger.Debugf("delLivestreamWithVideoId: lock:%s is exist", lockKey)
		return nil
	}

	defer func() {
		if err = s.redis.Del(lockKey).Err(); err != nil {
			s.logger.Errorf("delLivestreamWithVideoId: failed to del lock key: %s, err: %v", lockKey, err)
			return
		}
	}()

	liveStreaming, err := s.liveStreamRedisService.getLiveStreaming(deviceSn)
	if err != nil {
		s.logger.Errorf("get live streaming failed, deviceSn: %s, err: %v", deviceSn, err)
		return err
	}

	if liveStreaming == nil {
		return nil
	}

	liveStatusList := make([]dto.LiveStatusDTO, 0)

	for _, status := range liveStreaming.LiveStatus {
		if status.VideoId != videoId {
			liveStatusList = append(liveStatusList, status)
			break
		}
	}

	if len(liveStatusList) != len(liveStreaming.LiveStatus) {
		data := dto.DeviceLiveStreaming{
			GatewaySn:  liveStreaming.GatewaySn,
			LiveStatus: liveStatusList,
		}

		if err = s.liveStreamRedisService.setLiveStreaming(deviceSn, data); err != nil {
			s.logger.Errorf("set live streaming failed, err: %v", err)
			return err
		}
	}

	return nil
}

//func GetDockLiveStreamURL(stream string, streamType string) string {
//	srsConfig := config.GetConfig().Live.SrsDock
//	var streamUrl string
//
//	if streamType == "http" {
//		flvURL := srsConfig.FlvURL
//		streamUrl = fmt.Sprintf("%s%s.flv", flvURL, stream)
//	} else if streamType == "rtmp" {
//		rtmpURL := srsConfig.RtmpURL
//		secret := srsConfig.RtmpPushSecret
//		streamUrl = fmt.Sprintf("%s%s?secret=%s", rtmpURL, stream, secret)
//	}
//
//	return streamUrl
//}

func (s *LiveStreamService) LiveLensChange(gatewaySn string, videoId string, videoType cloud_enum.LensChangeVideoTypeEnum) (error, string) {
	req := request.LiveLensChangeRequest{
		VideoId:   videoId,
		VideoType: videoType,
	}

	res, err := cloudinfra.LiveLensChange(gatewaySn, req)
	if err != nil {
		return err, "修改相机模式失败"
	}

	if res.Data.Result != nil && *res.Data.Result != 0 {
		err = fmt.Errorf("set live lens failed, gatewaySn: %s, err code: %d", gatewaySn, *res.Data.Result)

		codeEnum := wayline_cloud_enum.FindWaylineErrorCodeEnum(*res.Data.Result)
		if codeEnum == wayline_cloud_enum.WaylineErrorCodeEnumUNKNOWN {
			return err, "修改相机模式失败"
		} else {
			return err, fmt.Sprintf("修改相机模式失败：%s", codeEnum.String())
		}
	}

	gateway, err := NewDeviceService().GetDeviceBySn(gatewaySn)
	if err != nil {
		s.logger.Errorf("LiveLensChange: get gateway failed, gatewaySn: %s, err: %v", gatewaySn, err)
		return nil, ""
	}

	drone, err := NewDroneService().GetDroneByVehicleID(gateway.ChildSN)
	if err != nil {
		s.logger.Errorf("LiveLensChange: get drone failed, droneSn: %s, err: %v", gateway.ChildSN, err)
		return nil, ""
	}

	jobType := enum.FindJobTypeEnum(drone.JobType)
	if jobType != enum.JobTypeEnum_Unknown && jobType != enum.JobTypeEnum_NoneJob {
		_ = NewJobLogsService().SaveJobLogByGateway(jobType, gateway.JobId, gatewaySn, "无人机相机模式修改成功", false)
	}

	return nil, ""
}

func (s *LiveStreamService) LiveSetQuality(gatewaySn string, videoId string, videoQuality cloud_enum.VideoQualityEnum) (error, string) {
	req := request.LiveSetQualityRequest{
		VideoId:      videoId,
		VideoQuality: videoQuality,
	}

	res, err := cloudinfra.LiveSetQuality(gatewaySn, req)
	if err != nil {
		return err, "修改画质失败"
	}

	if res.Data.Result != nil && *res.Data.Result != 0 {
		err = fmt.Errorf("set live quality failed, gatewaySn: %s, err code: %d", gatewaySn, *res.Data.Result)

		codeEnum := wayline_cloud_enum.FindWaylineErrorCodeEnum(*res.Data.Result)
		if codeEnum == wayline_cloud_enum.WaylineErrorCodeEnumUNKNOWN {
			return err, "修改画质失败"
		} else {
			return err, fmt.Sprintf("修改画质失败：%s", codeEnum.String())
		}
	}

	gateway, err := NewDeviceService().GetDeviceBySn(gatewaySn)
	if err != nil {
		s.logger.Errorf("LiveSetQuality: get gateway failed, gatewaySn: %s, err: %v", gatewaySn, err)
		return nil, ""
	}

	// 如果是遥控器则直接同步直播状态
	if gateway.Domain == device_cloud_enum.Gateway.Value() {
		err = s.updateLiveStreamingQualityStatus(gateway.ChildSN, videoId, videoQuality)
		if err != nil {
			s.logger.Errorf("LiveSetQuality: updateLiveStreamingQualityStatus failed, gatewaySn: %s, err: %v", gatewaySn, err)
			return nil, ""
		}
	}

	drone, err := NewDroneService().GetDroneByVehicleID(gateway.ChildSN)
	if err != nil {
		s.logger.Errorf("LiveSetQuality: get drone failed, droneSn: %s, err: %v", gateway.ChildSN, err)
		return nil, ""
	}

	jobType := enum.FindJobTypeEnum(drone.JobType)
	if jobType != enum.JobTypeEnum_Unknown && jobType != enum.JobTypeEnum_NoneJob {
		_ = NewJobLogsService().SaveJobLogByGateway(jobType, gateway.JobId, gatewaySn, "无人机相机视频画质修改成功", false)
	}

	return nil, ""
}

// updateLiveStreamingQualityStatus 修改直播状态中的quality
func (s *LiveStreamService) updateLiveStreamingQualityStatus(deviceSn string, videoId string, videoQuality cloud_enum.VideoQualityEnum) error {
	liveStreaming, err := s.liveStreamRedisService.getLiveStreaming(deviceSn)
	if err != nil {
		s.logger.Errorf("get live streaming failed, err: %v", err)
		return err
	}

	if liveStreaming == nil {
		return nil
	}

	newLiveStreaming := liveStreaming

	for i, oldStatus := range liveStreaming.LiveStatus {
		if oldStatus.VideoId == videoId {
			newLiveStreaming.LiveStatus[i].VideoQuality = videoQuality
			break
		}
	}

	if err = s.liveStreamRedisService.setLiveStreaming(deviceSn, *newLiveStreaming); err != nil {
		s.logger.Errorf("set live streaming failed, err: %v", err)
		return err
	}

	return nil
}
