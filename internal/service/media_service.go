package service

import (
	"errors"
	"fmt"
	"strings"
	"time"
	"wukong-api/internal/dto"
	"wukong-api/internal/enum"

	"github.com/google/uuid"

	"sync"
	"wukong-api/cloud_sdk/cloud_api/media/request"
	"wukong-api/cloud_sdk/mqtt"
	"wukong-api/internal/cloudinfra"
	"wukong-api/internal/fileds"
	"wukong-api/internal/model"
	"wukong-api/internal/repo"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	mediaServiceOnce sync.Once
	mediaService     *MediaService
)

type MediaService struct {
	db                *gorm.DB
	logger            *zap.SugaredLogger
	mediaRedisService *MediaRedisService
	waylineJobService *WaylineJobService
}

func NewMediaService() *MediaService {
	mediaServiceOnce.Do(func() {
		mediaService = &MediaService{
			db:                repo.GetDatabase(),
			logger:            repo.GetLogger(),
			mediaRedisService: NewMediaRedisService(),
			waylineJobService: NewWaylineJobService(),
		}
	})
	return mediaService
}

func (s *MediaService) FileUploadCallbackHandle(req mqtt.TopicEventsRequest[request.FileUploadCallback]) error {
	callback := req.Data

	if callback.Result != 0 {
		err := fmt.Errorf("FileUploadCallbackHandle: file upload failed, file name: %s, object key: %s", callback.File.Name, callback.File.ObjectKey)
		s.logger.Errorf(err.Error())
		return err
	}

	jobId := callback.File.Ext.FlightId
	gatewaySn := req.Gateway

	device, err := NewDeviceRedisService().getDeviceOnline(gatewaySn)
	if err != nil {
		s.logger.Errorf("FileUploadCallbackHandle: getDeviceOnline failed: dockSn: %s", gatewaySn)
		return err
	}

	if device == nil {
		s.logger.Warnf("FileUploadCallbackHandle: device or mediaFileCount is nil, jobId: %s, dockSn: %s", jobId, gatewaySn)

		err = cloudinfra.EventsReply(fileds.FILE_UPLOAD_CALLBACK, gatewaySn, req.Bid, req.Tid)
		if err != nil {
			s.logger.Errorf("FileUploadCallbackHandle, FileUploadCallbackReply failed, jobId: %s, err: %v", jobId, err)
			return err
		}
		return nil
	}

	job, err := s.waylineJobService.GetWaylineJobByJobId(jobId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 任务已经被删除
			//err = NewOssService().DelObject(callback.File.ObjectKey)
			//if err != nil {
			//	return err
			//}

			err = cloudinfra.EventsReply(fileds.FILE_UPLOAD_CALLBACK, gatewaySn, req.Bid, req.Tid)
			if err != nil {
				s.logger.Errorf("FileUploadCallbackHandle, FileUploadCallbackReply failed, jobId: %s, err: %v", jobId, err)
				return err
			}
			return nil
		} else {
			s.logger.Errorf("FileUploadCallbackHandle, GetWaylineJobByJobId failed, jobId: %s, err: %v", jobId, err)
			return err
		}
	}

	mediaFileCount, err := s.mediaRedisService.getMediaCount(gatewaySn, jobId)
	if err != nil {
		s.logger.Warnf("FileUploadCallbackHandle: getMediaCount failed, jobId: %s, dockSn: %s", jobId, gatewaySn)
		return err
	}

	if mediaFileCount == nil {
		mediaFileCount = &dto.MediaFileCountDTO{
			JobId:        jobId,
			MediaCount:   nil,
			FileInfoList: make([]dto.FileInfo, 0),
		}
	} else {
		// 检查文件名列表中是否重复，重复就跳过
		for _, fileInfo := range mediaFileCount.FileInfoList {
			if fileInfo.Filename == callback.File.Name {
				err = cloudinfra.EventsReply(fileds.FILE_UPLOAD_CALLBACK, gatewaySn, req.Bid, req.Tid)
				if err != nil {
					s.logger.Errorf("FileUploadCallbackHandle, FileUploadCallbackReply failed, jobId: %s, err: %v", jobId, err)
					return err
				}
				return nil
			}
		}
	}

	mediaFileId, err := s.saveMediaFileWithCallback(callback.File, *device, job.CompanyID, enum.JobTypeEnum_Wayline)
	if err != nil {
		return err
	}

	err = s.checkUploadedCount(mediaFileId, mediaFileCount, req, job)
	if err != nil {
		return err
	}

	err = cloudinfra.EventsReply(fileds.FILE_UPLOAD_CALLBACK, gatewaySn, req.Bid, req.Tid)
	if err != nil {
		s.logger.Errorf("FileUploadCallbackHandle, FileUploadCallbackReply failed, jobId: %s, err: %v", jobId, err)
		return err
	}

	return nil
}

func (s *MediaService) checkUploadedCount(mediaFileId int, mediaFileCount *dto.MediaFileCountDTO, req mqtt.TopicEventsRequest[request.FileUploadCallback], job *model.WaylineJob) error {
	jobId := job.JobID
	gatewaySn := req.Gateway
	filename := req.Data.File.Name
	isToPending := false

	jobRecordSvc := NewInspectionJobRecordService()
	jobRecord, err := jobRecordSvc.getByWaylineJobId(job.JobID)
	if err != nil {
		s.logger.Errorf("checkUploadedCount: get inspection job record failed, jobId: %s, dockSn: %s, err: %v", jobId, gatewaySn, err)
		return err
	}

	var shootTime int
	if req.Data.File.Metadata.CreatedTime != "" {
		tt, err := time.Parse(time.RFC3339, req.Data.File.Metadata.CreatedTime)
		if err != nil {
			s.logger.Errorf("checkUploadedCount: parse created time failed, file name: %s, err: %v", filename, err)
		} else {
			shootTime = int(tt.UnixMilli())
		}
	}

	fileInfo := dto.FileInfo{
		Filename:    filename,
		ShootTime:   shootTime,
		UploadTime:  req.Timestamp,
		MediaFileId: mediaFileId,
	}

	mediaFileCount.FileInfoList = append(mediaFileCount.FileInfoList, fileInfo)

	if mediaFileCount.MediaCount != nil && len(mediaFileCount.FileInfoList) >= *mediaFileCount.MediaCount {
		// 全部上传完成
		if err = NewMediaRedisService().delMediaCount(gatewaySn, jobId); err != nil {
			s.logger.Warnf("checkUploadedCount: del media count failed, jobId: %s, dockSn: %s, err: %v", jobId, gatewaySn, err)
		}

		// _ = NewJobLogsService().SaveJobLogByGateway(enum.JobTypeEnum_Wayline, job.JobID, gatewaySn, "任务产生的媒体资源全部上传完成", false)

		if job.Status == enum.Wayline_Success.Value() {
			if err = NewWaylineJobService().UpdateWaylineJobStatus(enum.Success.Value(), jobId, nil); err != nil {
				return err
			}
		}

		// 如果任务完成了则将状态改为待处理
		if !jobRecord.CompletedTime.IsZero() {
			isToPending = isJobProcessedStatusToPending(job.CompanyID, enum.FindJobSceneTypeEnum(jobRecord.Scene), enum.JobProcessedFileType_CameraTake)
		}
	} else {
		err = s.mediaRedisService.setMediaCount(gatewaySn, *mediaFileCount)
		if err != nil {
			s.logger.Errorf("checkUploadedCount: set media count failed, jobId: %s, dockSn: %s, err: %v", jobId, gatewaySn, err)
			return err
		}

		// 如果是云台可见光视频已上传
		if strings.Contains(filename, "_V.mp4") {
			isToPending = isJobProcessedStatusToPending(job.CompanyID, enum.FindJobSceneTypeEnum(jobRecord.Scene), enum.JobProcessedFileType_CameraTakeVideo)
		}
	}

	if isToPending {
		err = jobRecordSvc.UpdateProcessedStatus(jobRecord.ID, enum.JobProcessedStatusEnum_PendingAI)
		if err != nil {
			s.logger.Errorf("checkUploadedCount: update inspection job record failed, jobRecordID: %d, dockSn: %s, err: %v", jobRecord.ID, gatewaySn, err)
			return err
		}
	}

	return nil
}

func (s *MediaService) saveMediaFileWithCallback(file request.FileUploadCallbackFile, device dto.DeviceDTO, cid int, jobType enum.JobTypeEnum) (mediaFileId int, err error) {
	saveDto := dto.SaveMediaFileDto{
		FileName:     file.Name,
		OldObjectKey: file.ObjectKey,
		IsOriginal:   file.Ext.IsOriginal,
		Cid:          cid,
		JobId:        file.Ext.FlightId,
		DroneSn:      device.ChildSN,
		Lng:          fmt.Sprintf("%v", file.Metadata.ShootPosition.Lng),
		Lat:          fmt.Sprintf("%v", file.Metadata.ShootPosition.Lat),
		Payload:      file.Ext.PayloadModelKey,
		IsGimbal:     IsGimbalByDeviceModelKey(file.Ext.PayloadModelKey),
		CreatedTime:  file.Metadata.CreatedTime,
	}

	return s.saveMediaFile(saveDto, jobType)
}

func (s *MediaService) saveMediaFile(fileDto dto.SaveMediaFileDto, jobType enum.JobTypeEnum) (mediaFileId int, err error) {
	suffix := (strings.Split(fileDto.FileName, "."))[1]
	suffix = strings.ToLower(suffix)

	var ok bool

	var fileType int
	var newObjectKey string
	var uploadPathType string
	var createTime *time.Time

	if suffix == "jpg" || suffix == "jpeg" || suffix == "png" {
		fileType = 1
	} else if suffix == "mp4" || suffix == "flv" {
		fileType = 2
		uploadPathType = TaskRecordLive
	} else if suffix == "mrk" {
		fileType = 3
	} else if suffix == "nav" {
		fileType = 4
	} else if suffix == "obs" {
		fileType = 5
	} else if suffix == "rtk" {
		fileType = 6
	}

	if fileType == 1 || fileType == 2 {
		if fileDto.CreatedTime != "" {
			var tt time.Time
			tt, err = time.Parse(time.RFC3339, fileDto.CreatedTime)
			if err != nil {
				s.logger.Errorf("saveMediaFile: parse created time failed, file name: %s, err: %v", fileDto.FileName, err)
			} else {
				createTime = &tt
			}
		}
	}

	if uploadPathType == "" {
		uploadPathType = TaskRawResult
	}

	var uploadPathFunc UploadPathFormat
	uploadPathFunc, ok = UploadTypeMapping[uploadPathType]
	if !ok {
		return 0, fmt.Errorf("saveMediaFileWithCallback: upload path func not found, file type: %s", suffix)
	}

	newObjectKey, err = uploadPathFunc(nil, fileDto.Cid, fileDto.FileName, ExtInfo{JobId: fileDto.JobId})
	if err != nil {
		return 0, err
	}
	err = NewOssService().MoveObject(fileDto.OldObjectKey, newObjectKey)
	if err != nil {
		return 0, err
	}

	fileModel := model.MediaFile{
		FileID:     uuid.New().String(),
		FileName:   fileDto.FileName,
		CompanyID:  fileDto.Cid,
		ObjectKey:  newObjectKey,
		IsOriginal: fileDto.IsOriginal,
		Drone:      fileDto.DroneSn,
		Longitude:  fmt.Sprintf("%v", fileDto.Lng),
		Latitude:   fmt.Sprintf("%v", fileDto.Lat),
		FileType:   fileType,
		//FilePath:         ,
		//Fingerprint:      file.Fingerprint,
		//TinnyFingerprint: file.Ext.TinnyFingerprint,
		//SubFileType:      file.SubFileType.Value(),
		DeviceModelKey: fileDto.Payload,
		IsGimbal:       fileDto.IsGimbal,
	}

	if fileType == 2 {
		fileModel.RecordStartTime = createTime
	}

	if jobType == enum.JobTypeEnum_Wayline {
		fileModel.WaylineJobID = fileDto.JobId
	} else if jobType == enum.JobTypeEnum_Manual {
		fileModel.ManualJobId = fileDto.JobId
	}

	err = s.db.Create(&fileModel).Error
	if err != nil {
		return 0, err
	}

	if fileType == 1 {
		var warnDataList []dto.CreateWarnData
		warnDataList, err = NewCockpitRedisService().getCreateWarnDataList(fileDto.JobId)
		if err != nil {
			return 0, err
		}

		if warnDataList != nil && len(warnDataList) > 0 {
			// todo 找到时间匹配的拍照告警信息，生成告警记录，删缓存
		}
	}

	return fileModel.ID, nil
}

func (s *MediaService) getMediaFileCountByManualJobID(jobID string) (int64, error) {
	var count int64
	err := s.db.Model(&model.MediaFile{}).Where("manual_job_id = ?", jobID).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

// uploadFlighttaskMediaPrioritize 调整上传的文件为最高优先级
func (s *MediaService) uploadFlighttaskMediaPrioritize(jobId string, gatewaySn string) error {
	req := request.UploadFlighttaskMediaPrioritizeRequest{
		FlightId: jobId,
	}

	serviceReply, err := cloudinfra.UploadFlighttaskMediaPrioritize(gatewaySn, req)
	if err != nil {
		s.logger.Errorf("MediaService:UploadFlighttaskMediaPrioritize failed, jobId: %s, err: %v", jobId, err)
		return err
	}

	if *serviceReply.Data.Result != 0 {
		return fmt.Errorf("MediaService:UploadFlighttaskMediaPrioritize failed, jobId: %s, err code: %d", jobId, *serviceReply.Data.Result)
	}

	return nil
}

// ReadPicExif 读取图片exif数据
func (s *MediaService) ReadPicExif(mediaFileId int) {
	// todo 返回深度信息
}
