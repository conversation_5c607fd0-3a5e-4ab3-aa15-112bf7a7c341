package service

import (
	"errors"
	"strings"
	"time"
	"wukong-api/cloud_sdk/cloud_api/device/request"
	"wukong-api/internal/enum"
	"wukong-api/internal/model"
	"wukong-api/internal/repo"

	"github.com/thoas/go-funk"

	"github.com/qxsugar/pkg/dbx"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type JobLogsService struct {
	db     *gorm.DB
	logger *zap.SugaredLogger
}

func NewJobLogsService() *JobLogsService {
	return &JobLogsService{
		db:     repo.GetDatabase(),
		logger: repo.GetLogger(),
	}
}

var (
	ignoreLogs = []string{"航线任务暂停", "无人机被接管", "无人机紧急制动", "无人机一键返航", "无人机拍照成功", "无人机相机模式修改成功"}
)

func (s *JobLogsService) Create(jobLogs *model.JobLogs) error {
	return s.db.Create(jobLogs).Error
}

func (s *JobLogsService) SaveJobLogByGateway(jobType enum.JobTypeEnum, jobId string, gatewaySn string, desc string, isError bool) error {
	var jobRecord *model.InspectionJobRecord
	var err error

	if jobType == enum.JobTypeEnum_Manual {
		jobRecord, err = NewInspectionJobRecordService().getByManualJobId(jobId)
		if err != nil {
			return err
		}
	} else if jobType == enum.JobTypeEnum_Wayline {
		jobRecord, err = NewInspectionJobRecordService().getByWaylineJobId(jobId)
		if err != nil {
			return err
		}
	}

	if jobRecord == nil {
		return errors.New("job record not found")
	}

	// 桥梁绕飞航线先避免记录这些日志
	if (jobRecord.WaylineManageID == 214 || jobRecord.WaylineManageID == 268) && (funk.ContainsString(ignoreLogs, desc) || strings.Contains(desc, "航线任务执行异常")) {
		return nil
	}

	var lng, lat float64

	deviceRedisSvc := NewDeviceRedisService()
	gatewayIsOnline, _ := deviceRedisSvc.CheckDeviceOnline(gatewaySn)
	if gatewayIsOnline {
		if jobType == enum.JobTypeEnum_Manual {
			var osd *request.OsdRC
			osd, err = deviceRedisSvc.getRcOsd(gatewaySn)
			if err != nil {
				return err
			}

			if osd != nil {
				lng = osd.Longitude
				lat = osd.Latitude
			}
		} else if jobType == enum.JobTypeEnum_Wayline {
			var osd *request.OsdDock
			osd, err = deviceRedisSvc.getDockOsd(gatewaySn)
			if err != nil {
				return err
			}

			if osd != nil {
				lng = osd.Longitude
				lat = osd.Latitude
			}
		}
	}

	var gateway *model.Device
	gateway, err = NewDeviceService().GetDeviceBySn(gatewaySn)
	if err != nil {
		return err
	}

	jobLog := model.JobLogs{
		JobRecordID:     jobRecord.ID,
		WaylineManageID: jobRecord.WaylineManageID,
		DroneID:         0,
		DeviceId:        gateway.ID,
		CompanyID:       jobRecord.CompanyID,
		Desc:            desc,
		Longitude:       lng,
		Latitude:        lat,
		DoTime:          dbx.TimeStamp{Time: time.Now()},
		IsError:         isError,
	}

	if err = s.Create(&jobLog); err != nil {
		return err
	}

	return nil
}

func (s *JobLogsService) SaveJobLogByDrone(waylineJobId string, dockSn string, desc string) error {
	jobRecord, err := NewInspectionJobRecordService().getByWaylineJobId(waylineJobId)
	if err != nil {
		return err
	}

	dev, err := NewDeviceService().GetDeviceBySn(dockSn)
	if err != nil {
		return err
	}

	drone, err := NewDroneService().GetDroneByVehicleID(dev.ChildSN)
	if err != nil {
		return err
	}

	// fixme
	//droneBaseInfo, err := NewDroneService().GetDroneBaseInfo(dev.ChildSN)
	//if err != nil {
	//	return err
	//}

	jobLog := model.JobLogs{
		JobRecordID:     jobRecord.ID,
		WaylineManageID: jobRecord.WaylineManageID,
		DroneID:         drone.ID,
		DeviceId:        0,
		CompanyID:       jobRecord.CompanyID,
		Desc:            desc,
		//Longitude:       droneBaseInfo.GPSPosition.Longitude,
		//Latitude:        droneBaseInfo.GPSPosition.Latitude,
		DoTime: dbx.TimeStamp{Time: time.Now()},
	}

	if err = s.Create(&jobLog); err != nil {
		return err
	}

	return nil
}
