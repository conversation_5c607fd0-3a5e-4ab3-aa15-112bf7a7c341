package service

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"math/rand"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	//device_request "wukong-api/cloud_sdk/cloud_api/device/request"
	"wukong-api/cloud_sdk/cloud_api/media/request"
	"wukong-api/internal/client"
	"wukong-api/internal/config"
	"wukong-api/internal/dto"
	"wukong-api/internal/enum"
	"wukong-api/internal/fileds"
	"wukong-api/internal/model"
	"wukong-api/internal/pb"
	"wukong-api/internal/repo"
	"wukong-api/pkg/utils"

	"github.com/google/uuid"

	"github.com/thoas/go-funk"

	"google.golang.org/grpc"

	"github.com/qxsugar/pkg/dbx"

	"github.com/go-redis/redis"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	jobDataProcessorServiceOnce sync.Once
	jobDataProcessorService     *JobDataProcessorService
	reportCids                  = []int{88, 91, 1, 93, 94}
)

type JobDataProcessorService struct {
	db                 *gorm.DB
	logger             *zap.SugaredLogger
	redis              *redis.Client
	deviceRedisService *DeviceRedisService
}

func NewJobDataProcessorService() *JobDataProcessorService {
	jobDataProcessorServiceOnce.Do(func() {
		jobDataProcessorService = &JobDataProcessorService{
			db:                 repo.GetDatabase(),
			logger:             repo.GetLogger(),
			redis:              repo.GetRedis(0),
			deviceRedisService: NewDeviceRedisService(),
		}
	})
	return jobDataProcessorService
}

// JobDataWorker 作业数据处理
func JobDataWorker() {
	service := NewJobDataProcessorService()
	go func() {
		ticker := time.NewTicker(3 * time.Second)
		for {
			<-ticker.C
			service.handleRealTimeJobData()
		}
	}()

	go func() {
		ticker := time.NewTicker(5 * time.Second)
		for {
			<-ticker.C
			service.handleNoRealTimeJobData()
		}
	}()

	go func() {
		ticker := time.NewTicker(3 * time.Second)
		for {
			<-ticker.C
			service.handleRealTimeControlConnectionReq()
		}
	}()
}

// handleRealTimeControlConnectionReq 建立实时控制请求
func (s *JobDataProcessorService) handleRealTimeControlConnectionReq() {
	if CheckIsOccupiedPermissions() {
		return
	}

	droneList, err := NewDroneService().GetDroneList()
	if err != nil {
		s.logger.Errorf("handleRealTimeControlConnectionReq: failed to get drone list: %v", err)
		return
	}

	dockList, err := NewDeviceService().getAllDock()
	if err != nil {
		s.logger.Errorf("handleRealTimeControlConnectionReq: failed to get dock list: %v", err)
		return
	}

	lockKeyPattern := fmt.Sprintf(fileds.RealTimeControlLock, "*")
	keyList, _ := s.redis.Keys(lockKeyPattern).Result()

	// 释放已经结束的任务处理
	for _, key := range keyList {
		waylineJobId, _ := s.redis.Get(key).Result()
		if waylineJobId == "" {
			continue
		}

		flag := false

		for _, drone := range droneList {
			if drone.WaylineJobId == waylineJobId {
				flag = true
				break
			}
		}

		if flag {
			continue
		}

		// 如果此时还有锁没匹配上，则表示作业已经结束
		s.logger.Debugf("handleRealTimeControlConnectionReq: end real-time control req, waylineJobId: %s", waylineJobId)
		go func(lockKey string) {
			connId := strings.Split(lockKey, fileds.RealTimeControlLockPrefix)[1]
			dockSn := strings.Split(connId, fmt.Sprintf("%s-", fileds.ServerRealTimeControl))[1]

			err = s.endRealTimeControlConnection(lockKey, dockSn, connId)
			if err != nil {
				s.logger.Errorf("handleRealTimeControlConnectionReq: request real-time control req failed, waylineJobId: %s, err: %v", waylineJobId, err)
			}
		}(key)
	}

	// 开启进行中的任务处理
	for _, drone := range droneList {
		if drone.WaylineJobId != "" {
			var dockSn string
			for _, dock := range dockList {
				if dock.ChildSN == drone.VehicleId {
					dockSn = dock.DeviceSN
					break
				}
			}

			if dockSn == "" {
				s.logger.Errorf("handleRealTimeControlConnectionReq: failed to get dock sn, drone sn: %s", drone.VehicleId)
				continue
			}

			connId := fmt.Sprintf("%s-%s", fileds.ServerRealTimeControl, dockSn)
			lockKey := fmt.Sprintf(fileds.RealTimeControlLock, connId)
			flag := false

			// 找到匹配的锁，则表示正在处理，跳过
			for _, key := range keyList {
				if lockKey == key {
					flag = true
					break
				}
			}

			if flag {
				continue
			}

			// 没有匹配的则发起处理
			s.logger.Debugf("handleRealTimeControlConnectionReq: request real-time control req, waylineJobId: %s", drone.WaylineJobId)
			go func(droneSn, waylineJobId string) {
				err = s.requestRealTimeControlConnection(droneSn, dockSn, lockKey, waylineJobId, connId)
				if err != nil {
					s.logger.Debugf("handleRealTimeControlConnectionReq: request real-time control req failed, waylineJobId: %s, err: %v", waylineJobId, err)
				}
			}(drone.VehicleId, drone.WaylineJobId)
		}
	}
}

// requestRealTimeControlConnection 请求实时控制连接
func (s *JobDataProcessorService) requestRealTimeControlConnection(droneSn, dockSn, lockKey, waylineJobId, connID string) error {
	drone, err := NewDeviceService().GetDeviceBySn(droneSn)
	if err != nil {
		return err
	}

	wsManager := repo.NewWsManager()
	LiveStreamServerSvc := NewLiveStreamServerService()
	LiveStreamSvc := NewLiveStreamService()

	// 检查websocket连接
	if _, ok := wsManager.WsConn[connID]; !ok {
		err = fmt.Errorf("requestRealTimeControlConnection: failed to get websocket connection, or connection closed. connID: %s", connID)
		s.logger.Debugf(err.Error())
		return err
	}

	droneLiveCapacity, err := LiveStreamSvc.GetDeviceLiveStreamURL(droneSn)
	if err != nil {
		s.logger.Errorf("requestRealTimeControlConnection: failed to get drone live stream, sn: %s, err: %v", droneSn, err)
		return err
	}

	var pullStreamUrl, cameraIndex string
	if droneLiveCapacity != nil {
		for _, liveCapacity := range droneLiveCapacity {
			if IsGimbalByPayloadModelKey(liveCapacity.CameraIndex) {
				cameraIndex = liveCapacity.CameraIndex
				pullStreamUrl = liveCapacity.StreamURL
				break
			}
		}
	}

	if pullStreamUrl == "" {
		err = fmt.Errorf("requestRealTimeControlConnection: failed to get drone live stream, sn: %s", droneSn)
		s.logger.Errorf(err.Error())
		return err
	}

	var liveStreamServer model.LiveStreamServer
	err = s.db.Where("id = ?", drone.LiveStreamServerID).First(&liveStreamServer).Error
	if err != nil {
		return err
	}

	stream := fmt.Sprintf("%s_%s_%s_%s", droneSn, cameraIndex, waylineJobId, fileds.Control)

	controlPushStreamUrl, err := LiveStreamServerSvc.genLiveStreamUrlWithRecord(liveStreamServer, stream, enum.LiveStreamType_Push)
	if err != nil {
		return err
	}

	controlPullStreamUrl, err := LiveStreamServerSvc.genLiveStreamUrlWithRecord(liveStreamServer, stream, enum.LiveStreamType_Pull)
	if err != nil {
		return err
	}

	s.logger.Infof("controlPullStreamUrl: %s", controlPullStreamUrl)

	// 如果pullStreamUrl是artc格式，则转换为http的flv格式
	if strings.HasPrefix(pullStreamUrl, "artc://") {
		// artc://host/app/stream?auth_key=xxx => http://host/app/stream.flv?auth_key=xxx
		urlWithoutScheme := strings.TrimPrefix(pullStreamUrl, "artc://")
		parts := strings.SplitN(urlWithoutScheme, "?", 2)
		path := parts[0]
		// 插入.flv
		if strings.Contains(path, ".flv") {
			// 已经是flv格式
			pullStreamUrl = "http://" + path
		} else {
			pullStreamUrl = "http://" + path + ".flv"
		}
		key := liveStreamServer.PullSecretKey
		protocol := "http"
		exp := time.Now().Unix() + 3600
		pullStreamUrl = aliyunLiveToAuth(pullStreamUrl, key, exp, protocol)
	}

	req := dto.JobDataProcessingReq{
		Type: fileds.StartRealTimeControlReq,
		Data: dto.StartRealTimeControlReq{
			PullStreamUrl: pullStreamUrl,
			PushStreamUrl: controlPushStreamUrl,
			JobID:         waylineJobId,
			TargetClasses: []string{},
			DroneSn:       droneSn,
			DockSn:        dockSn,
		},
	}

	var bytes []byte
	if bytes, err = json.Marshal(&req); err != nil {
		s.logger.Errorf("requestRealTimeControlConnection: failed to encode data, err: %v", err)
		return err
	}

	// 上锁
	var acquired bool
	acquired, err = s.redis.SetNX(lockKey, waylineJobId, fileds.RealTimeControlLockTimeout).Result()
	if err != nil {
		s.logger.Errorf("requestRealTimeControlConnection: failed to lock. lockKey: %s, err: %v", lockKey, err)
		return err
	}
	if !acquired {
		err = fmt.Errorf("requestRealTimeControlConnection: failed to lock. lockKey: %s", lockKey)
		s.logger.Errorf(err.Error())
		return err
	}

	// 发送请求
	wsManager.SendData(bytes, connID)

	return nil
}

// endRealTimeControlConnection 关闭实时控制连接
func (s *JobDataProcessorService) endRealTimeControlConnection(lockKey, dockSn, connID string) error {
	defer func() {
		// 释放锁
		aiControlLockKey := fmt.Sprintf(fileds.AI_Control_Lock, dockSn)

		if err := s.redis.Del(lockKey, aiControlLockKey).Err(); err != nil {
			s.logger.Errorf("endRealTimeControlConnection: del lock key: %s, err: %v", lockKey, err)
			return
		}
	}()

	wsManager := repo.NewWsManager()

	// 检查websocket连接
	if _, ok := wsManager.WsConn[connID]; !ok {
		err := fmt.Errorf("endRealTimeControlConnection: failed to get websocket connection, or connection closed. connID: %s", connID)
		s.logger.Errorf(err.Error())
		return err
	}

	// 通知结束
	req := dto.JobDataProcessingReq{
		Type: fileds.EndRealTimeControlReq,
		Data: nil,
	}

	bytes, err := json.Marshal(&req)
	if err != nil {
		s.logger.Errorf("endRealTimeControlConnection: failed to encode data, err: %v", err)
		return err
	}

	wsManager.SendData(bytes, connID)

	return nil
}

// handleRealTimeControl 处理实时控制响应消息
func (s *JobDataProcessorService) handleRealTimeControlResp(isSucceeded bool, data dto.RealTimeControlResp) {
	//connID := fmt.Sprintf("%s-%s", fileds.ServerRealTimeControl, data.DockSn)

	gatewaySn := data.DockSn

	inspectionStatusSvc := NewInspectionStatusService()
	inspectionStatus, err := inspectionStatusSvc.getInspectionStatus(gatewaySn)
	if err != nil {
		return
	}

	if !isSucceeded {
		// todo v3 异常处理
		s.logger.Warnf("handleRealTimeControlResp: predict control server handle data failed.")
		return
	}

	if data.Type == 1 {
		// 完成单次绕飞巡检，算法将控制权转交
		// 流转状态
		inspectionStatus.Status.CurrentStep = enum.InspectionStatusStepEnum_After
		inspectionStatus.DepthControlEnabled = false
		err = inspectionStatusSvc.setInspectionStatus(gatewaySn, inspectionStatus)
		if err != nil {
			return
		}

		// 释放锁
		executeDecisionLockKey := fmt.Sprintf(fileds.ExecuteDecisionLock, gatewaySn)
		s.redis.Del(executeDecisionLockKey)
	}
}

// handleRealTimeJobData 处理实时作业数据
func (s *JobDataProcessorService) handleRealTimeJobData() {
	if CheckIsOccupiedPermissions() {
		return
	}

	droneList, err := NewDroneService().GetDroneList()
	if err != nil {
		s.logger.Errorf("handleRealTimeJobData: failed to get drone list: %v", err)
		return
	}

	dockList, err := NewDeviceService().getAllDock()
	if err != nil {
		s.logger.Errorf("handleRealTimeJobData: failed to get dock list: %v", err)
		return
	}

	lockKeyPattern := fmt.Sprintf(fileds.RealTimeJobDataLock, "*")
	keyList, _ := s.redis.Keys(lockKeyPattern).Result()

	// 释放已经结束的任务处理
	for _, key := range keyList {
		waylineJobId, _ := s.redis.Get(key).Result()
		if waylineJobId == "" {
			continue
		}

		flag := false

		for _, drone := range droneList {
			if drone.WaylineJobId == waylineJobId {
				flag = true
				break
			}
		}

		if flag {
			continue
		}

		// 如果此时还有锁没匹配上，则表示作业已经结束
		s.logger.Debugf("handleRealTimeJobData: end real-time job data processing, waylineJobId: %s", waylineJobId)
		go func(lockKey string) {
			connId := strings.Split(lockKey, fileds.RealTimeJobDataLockPrefix)[1]
			dockSn := strings.Split(connId, fmt.Sprintf("%s-", fileds.ServerRealTime))[1]

			err = s.endRealTimeDataProcessing(lockKey, dockSn, connId)
			if err != nil {
				s.logger.Errorf("handleRealTimeJobData: request real-time job data processing failed, waylineJobId: %s, err: %v", waylineJobId, err)
			}
		}(key)
	}

	// 开启进行中的任务处理
	for _, drone := range droneList {
		if drone.WaylineJobId != "" {
			var dockSn string
			for _, dock := range dockList {
				if dock.ChildSN == drone.VehicleId {
					dockSn = dock.DeviceSN
					break
				}
			}

			if dockSn == "" {
				s.logger.Errorf("handleRealTimeJobData: failed to get dock sn, drone sn: %s", drone.VehicleId)
				continue
			}

			connId := fmt.Sprintf("%s-%s", fileds.ServerRealTime, dockSn)
			lockKey := fmt.Sprintf(fileds.RealTimeJobDataLock, connId)
			flag := false

			// 找到匹配的锁，则表示正在处理，跳过
			for _, key := range keyList {
				if lockKey == key {
					flag = true
					break
				}
			}

			if flag {
				continue
			}

			// 没有匹配的则发起处理
			s.logger.Debugf("handleRealTimeJobData: request real-time job data processing, waylineJobId: %s", drone.WaylineJobId)
			go func(droneSn, waylineJobId string) {
				err = s.requestRealTimeDataProcessing(droneSn, dockSn, lockKey, waylineJobId, connId)
				if err != nil {
					s.logger.Debugf("handleRealTimeJobData: request real-time job data processing failed, waylineJobId: %s, err: %v", waylineJobId, err)
				}
			}(drone.VehicleId, drone.WaylineJobId)
		}
	}
}

// requestRealTimeDataProcessing 向算法服务请求处理实时数据
func (s *JobDataProcessorService) requestRealTimeDataProcessing(droneSn, dockSn, lockKey, waylineJobId, connID string) error {
	drone, err := NewDeviceService().GetDeviceBySn(droneSn)
	if err != nil {
		return err
	}

	wsManager := repo.NewWsManager()
	LiveStreamSvc := NewLiveStreamServerService()

	// 检查websocket连接
	if _, ok := wsManager.WsConn[connID]; !ok {
		err = fmt.Errorf("requestRealTimeDataProcessing: failed to get websocket connection, or connection closed. connID: %s", connID)
		s.logger.Debugf(err.Error())
		return err
	}

	droneLiveCapacity, err := NewLiveStreamService().GetDeviceLiveStreamURL(droneSn)
	if err != nil {
		s.logger.Errorf("requestRealTimeDataProcessing: failed to get drone live stream, sn: %s, err: %v", droneSn, err)
		return err
	}

	var pullStreamUrl, cameraIndex string
	if droneLiveCapacity != nil {
		for _, liveCapacity := range droneLiveCapacity {
			if IsGimbalByPayloadModelKey(liveCapacity.CameraIndex) {
				cameraIndex = liveCapacity.CameraIndex
				pullStreamUrl = liveCapacity.StreamURL
				break
			}
		}
	}

	if pullStreamUrl == "" {
		err = fmt.Errorf("requestRealTimeDataProcessing: failed to get drone live stream, sn: %s", droneSn)
		s.logger.Errorf(err.Error())
		return err
	}

	// 用于调试
	//pullStreamUrl := GetAliyunLiveStreamURL("local", "http")

	// 根据选择的直播服务器来获取推流地址 参数见model/visualization 1-ali 5-srs ..
	res, _ := s.redis.Get(fileds.Visualization_LIVE_STREAMING).Result()

	var streamType int
	if res != "" {
		streamType, _ = strconv.Atoi(res)
	}

	var liveStreamServer model.LiveStreamServer
	err = s.db.Where("id = ?", drone.LiveStreamServerID).First(&liveStreamServer).Error
	if err != nil {
		return err
	}

	var processedPushStreamUrl string
	var processedPullStreamUrl string
	stream := fmt.Sprintf("%s_%s_%s_%s", droneSn, cameraIndex, waylineJobId, fileds.Processed)
	if streamType == 5 {
		processedPushStreamUrl = GetSRSLiveStreamURL(stream, "rtmp")
		processedPullStreamUrl = GetSRSLiveStreamURL(stream, "http")
	} else {
		//processedPushStreamUrl = GetAliyunLiveStreamURL(stream, "rtmp")
		//processedPullStreamUrl  = GetAliyunLiveStreamURL(stream, "http")

		processedPushStreamUrl, err = LiveStreamSvc.genLiveStreamUrlWithRecord(liveStreamServer, stream, enum.LiveStreamType_Push)
		if err != nil {
			return err
		}

		processedPullStreamUrl, err = LiveStreamSvc.genLiveStreamUrlWithRecord(liveStreamServer, stream, enum.LiveStreamType_Pull)
		if err != nil {
			return err
		}
	}

	jobRecord, err := NewInspectionJobRecordService().getByWaylineJobId(waylineJobId)
	if err != nil {
		s.logger.Errorf("requestRealTimeDataProcessing: failed to get job record, waylineJobID: %s, err: %v", waylineJobId, err)
		return err
	}

	var targetClasses []string
	if jobRecord.AlgorithmTarget != "" {
		if err = json.Unmarshal([]byte(jobRecord.AlgorithmTarget), &targetClasses); err != nil {
			s.logger.Errorf("requestRealTimeDataProcessing: failed to decode AlgorithmTarget, err: %v", err)
			return err
		}
	} else {
		// fixme 兼容老数据，后续可以去掉
		company := model.Company{}
		err = s.db.Model(model.Company{}).Where(model.Company{ID: jobRecord.CompanyID}).First(&company).Error
		if err != nil {
			s.logger.Errorf("requestRealTimeDataProcessing: failed to get company, cid: %d, err: %v", jobRecord.CompanyID, err)
			return err
		}
		if company.AlgorithmTarget != "" {
			if err = json.Unmarshal([]byte(company.AlgorithmTarget), &targetClasses); err != nil {
				s.logger.Errorf("requestRealTimeDataProcessing: failed to decode AlgorithmTarget, err: %v", err)
				return err
			}
		}
	}

	// 如果pullStreamUrl是artc格式，则转换为http的flv格式
	if strings.HasPrefix(pullStreamUrl, "artc://") {
		// artc://host/app/stream?auth_key=xxx => http://host/app/stream.flv?auth_key=xxx
		urlWithoutScheme := strings.TrimPrefix(pullStreamUrl, "artc://")
		parts := strings.SplitN(urlWithoutScheme, "?", 2)
		path := parts[0]
		// 插入.flv
		if strings.Contains(path, ".flv") {
			// 已经是flv格式
			pullStreamUrl = "http://" + path
		} else {
			pullStreamUrl = "http://" + path + ".flv"
		}
		key := liveStreamServer.PullSecretKey
		protocol := "http"
		exp := time.Now().Unix() + 3600
		pullStreamUrl = aliyunLiveToAuth(pullStreamUrl, key, exp, protocol)
	}

	uploadPathFunc, ok := UploadTypeMapping[TaskRawResult]
	if !ok {
		return fmt.Errorf("uploadType:%s is undefined", TaskRawResult)
	}

	path, err := uploadPathFunc(nil, jobRecord.CompanyID, "", ExtInfo{JobId: waylineJobId})
	if err != nil {
		return err
	}

	req := dto.JobDataProcessingReq{
		Type: fileds.StartRealTimeReq,
		Data: dto.StartRealTimeReq{
			PullStreamUrl:   pullStreamUrl,
			PushStreamUrl:   processedPushStreamUrl,
			JobID:           waylineJobId,
			TargetClasses:   targetClasses,
			DroneSn:         droneSn,
			ObjectKeyPrefix: path,
		},
	}

	var bytes []byte
	if bytes, err = json.Marshal(&req); err != nil {
		s.logger.Errorf("requestRealTimeDataProcessing: failed to encode data, err: %v", err)
		return err
	}

	// 上锁
	var acquired bool
	acquired, err = s.redis.SetNX(lockKey, waylineJobId, fileds.RealTimeLockTimeout).Result()
	if err != nil {
		s.logger.Errorf("requestRealTimeDataProcessing: failed to lock. lockKey: %s, err: %v", lockKey, err)
		return err
	}
	if !acquired {
		err = fmt.Errorf("requestRealTimeDataProcessing: failed to lock. lockKey: %s", lockKey)
		s.logger.Errorf(err.Error())
		return err
	}

	err = NewLiveStreamRedisService().setLiveStreamingProcessing(dockSn, processedPullStreamUrl, time.Now().Unix())
	if err != nil {
		return err
	}

	// 发送请求
	wsManager.SendData(bytes, connID)

	return nil
}

// endRealTimeDataProcessing 结束实时数据处理
func (s *JobDataProcessorService) endRealTimeDataProcessing(lockKey, dockSn, connID string) error {
	defer func() {
		// 释放锁
		if err := s.redis.Del(lockKey).Err(); err != nil {
			s.logger.Errorf("endRealTimeDataProcessing: del lock key: %s, err: %v", lockKey, err)
			return
		}
	}()

	wsManager := repo.NewWsManager()

	// 检查websocket连接
	if _, ok := wsManager.WsConn[connID]; !ok {
		err := fmt.Errorf("endRealTimeDataProcessing: failed to get websocket connection, or connection closed. connID: %s", connID)
		s.logger.Errorf(err.Error())
		return err
	}

	// 通知结束
	req := dto.JobDataProcessingReq{
		Type: fileds.EndRealTimeReq,
		Data: nil,
	}

	bytes, err := json.Marshal(&req)
	if err != nil {
		s.logger.Errorf("endRealTimeDataProcessing: failed to encode data, err: %v", err)
		return err
	}

	err = NewLiveStreamRedisService().delLiveStreamingProcessing(dockSn)
	if err != nil {
		return err
	}

	wsManager.SendData(bytes, connID)

	return nil
}

// handleNoRealTimeJobData 处理非实时作业数据
func (s *JobDataProcessorService) handleNoRealTimeJobData() {
	if CheckIsOccupiedPermissions() {
		return
	}

	if !CheckIsDoNoRealTime() {
		return
	}

	lockKeyPattern := fmt.Sprintf(fileds.NoRealTimeJobDataLock, "*")
	keyList, _ := s.redis.Keys(lockKeyPattern).Result()

	if len(keyList) > 0 {
		key := keyList[0]
		split := strings.Split(key, fileds.NoRealTimeJobDataLockPrefix)

		jobRecordID, err := strconv.Atoi(split[1])
		if err != nil {
			s.logger.Errorf("handleNoRealTimeJobData: failed to convert job record id: %s, err: %v", split[1], err)
			return
		}

		var count int64
		err = s.db.Model(model.InspectionJobRecord{}).Where("id = ?", jobRecordID).Where("processed_status", enum.JobProcessedStatusEnum_Processed.Value()).Count(&count).Error
		if err != nil {
			s.logger.Errorf("handleNoRealTimeJobData: failed to count video: %v", err)
			return
		}

		// 表示还在处理
		if count == 0 {
			s.logger.Debugf("handleNoRealTimeJobData: no real-time job data is processing, lock key: %s", key)
			return
		}

		// 处理完了，释放锁
		if err = s.redis.Del(key).Err(); err != nil {
			s.logger.Errorf("handleNoRealTimeJobData: del lock key: %s, err: %v", key, err)
			return
		}
	}

	// 获取待处理（资源已完成上传）的record
	needCid := GetNoRealTimeCompany()

	var jobRecord model.InspectionJobRecord
	recordOp := s.db.Model(model.InspectionJobRecord{}).Where("processed_status in (?)", []int{enum.JobProcessedStatusEnum_PendingAI.Value(), enum.JobProcessedStatusEnum_PendingReport.Value()})
	//recordOp := s.db.Model(model.InspectionJobRecord{}).Where("processed_status in (?)", []int{enum.JobProcessedStatusEnum_PendingAI.Value()})

	if needCid != 0 {
		recordOp = recordOp.Where("company_id = ?", needCid)
	}

	recordOp = recordOp.Order("id DESC").First(&jobRecord)
	err := recordOp.Error

	if err != nil {
		s.logger.Debugf("handleNoRealTimeJobData: no record pending, err: %v", err)
		return
	}

	// fixme 演示逻辑，214航线是桥梁绕飞航线
	if jobRecord.ProcessedStatus == enum.JobProcessedStatusEnum_PendingReport.Value() || jobRecord.WaylineManageID == 214 {
		// 直接生成报告
		s.logger.Infof("handleNoRealTimeJobData: jobRecord: %d, 直接生成报告", jobRecord.ID)

		// 目前只让这个公司生成报告
		if funk.Contains(reportCids, jobRecord.CompanyID) {
			var docxObjectKey string
			var jobWarnRecordDtoList []dto.JobWarnRecordDto
			var points []dto.KMLPoint

			err = json.Unmarshal([]byte(jobRecord.TargetTrajectory), &points)
			if err != nil {
				s.logger.Errorf("handleNoRealTimeJobData: failed to decode target trajectory, err: %v", err)
				return
			}

			docxObjectKey, err = getReport(jobRecord, jobWarnRecordDtoList, points)
			if err != nil {
				s.logger.Errorf("handleNoRealTimeJobData: jobRecord: %d, failed to get report, err: %v", jobRecord.ID, err)
				return
			}

			columns := map[string]interface{}{
				"report_docx_object_key": docxObjectKey,
				"processed_status":       enum.JobProcessedStatusEnum_Processed.Value(),
			}

			err = s.db.Model(model.InspectionJobRecord{}).Where("id = ?", jobRecord.ID).Updates(columns).Error
			if err != nil {
				s.logger.Errorf("handleNoRealTimeJobData: failed to set report_object_key, err: %v", err)
				return
			}

			// fixme 演示逻辑，添加刻度读数图片
			if jobRecord.WaylineManageID == 214 {
				fileModel := model.MediaFile{
					FileID:         uuid.New().String(),
					FileName:       "scale-reading.png",
					CompanyID:      94,
					ObjectKey:      "privateStorage/wukong/94/scale-reading.png",
					IsOriginal:     true,
					IsWarn:         false,
					Drone:          "1581F8HGX252F00A013M",
					Longitude:      fmt.Sprintf("%v", 118.20445),
					Latitude:       fmt.Sprintf("%v", 24.63414),
					WaylineJobID:   jobRecord.WaylineJobID,
					DeviceModelKey: "1-99-0",
					FileType:       1,
					IsGimbal:       true,
				}

				err = s.db.Create(&fileModel).Error
				if err != nil {
					return
				}
			}
		}
		return
	} else {
		// 算法识别

		// 根据不同公司和场景，判断处理图片还是视频
		isPic := false

		if jobRecord.Scene != 0 {
			typeEnum := enum.FindJobSceneTypeEnum(jobRecord.Scene)
			switch typeEnum {
			case enum.JobSceneTypeEnum_Bridge:
				isPic = true
			case enum.JobSceneTypeEnum_Road:
				isPic = true
			}
		}

		// 获取需要处理的视频或图片
		mediaList := make([]model.MediaFile, 0)
		mediaOp := s.db.Model(&model.MediaFile{}).
			Select("media_file.*").
			Where("media_file.wayline_job_id = ?", jobRecord.WaylineJobID)

		if isPic {
			// 图片
			err = mediaOp.Where("media_file.file_type = ?", 1).Where("file_name NOT LIKE ?", "%_T%").Order("media_file.id DESC").Find(&mediaList).Error
			if err != nil {
				s.logger.Errorf("handleNoRealTimeJobData: failed to select picture, err: %v", err)
				return
			}
		} else {
			// 视频
			mediaOp = mediaOp.Where("media_file.file_type = ? AND media_file.is_gimbal = 1 AND media_file.is_warn = 0", 2)

			if jobRecord.CompanyID == 91 || jobRecord.CompanyID == 1 {
				mediaOp = mediaOp.Where("media_file.file_name like ?", dbx.LikeString(".mp4"))
			} else if needCid == 93 {

			} else if needCid == 94 {
				//mediaOp = mediaOp.Where("media_file.file_name like ?", dbx.LikeString(".mp4")).Where("media_file.is_original = ?", false)
				mediaOp = mediaOp.Where("media_file.file_name like ?", dbx.LikeString("_V.mp4"))
			} else {
				mediaOp = mediaOp.Where("media_file.file_name like ?", dbx.LikeString(".mp4"))
			}

			err = mediaOp.Order("media_file.id DESC").Find(&mediaList).Error
			if err != nil {
				s.logger.Errorf("handleNoRealTimeJobData: failed to select videos, err: %v", err)
				return
			}
		}

		if mediaList == nil || len(mediaList) == 0 {
			// 未获取到资源，则标记为待生成报告
			s.logger.Infof("mediaList is not found, %d", jobRecord.ID)
			err = NewInspectionJobRecordService().UpdateProcessedStatus(jobRecord.ID, enum.JobProcessedStatusEnum_PendingReport)
			if err != nil {
				s.logger.Errorf("HandleNoRealTimeDataResp: failed to set processed_status, err: %v", err)
				return
			}
			return
		}

		var reqType string
		var reqData interface{}

		if isPic {
			reqType = fileds.StartNoRealTimePicReq

			picObjectKeyList := make([]string, 0)
			for _, media := range mediaList {
				picObjectKeyList = append(picObjectKeyList, media.ObjectKey)
			}

			reqData = dto.PicDataReq{
				PicObjectKeyList: picObjectKeyList,
			}
		} else {
			reqType = fileds.StartNoRealTimeVideoReq

			// todo 后续可能需要处理多视频
			video := mediaList[0]

			var startTime int64
			// fixme 以下判断是为了兼容老数据
			if video.RecordStartTime != nil && !video.RecordStartTime.IsZero() {
				startTime = video.RecordStartTime.UnixMilli()
			} else if !jobRecord.LiveStartTime.IsZero() {
				startTime = jobRecord.LiveStartTime.UnixMilli()
			} else if !jobRecord.ExecuteTime.IsZero() {
				startTime = jobRecord.ExecuteTime.UnixMilli()
			}

			var videoURL string
			videoURL, err = NewOssService().GetObjectSignURL(video.ObjectKey, 60*60)
			if err != nil {
				s.logger.Errorf("handleNoRealTimeJobData: failed to get object sign url, objectKey: %s, err: %v", video.ObjectKey, err)
				return
			}

			reqData = dto.VideoDataReq{
				LiveStartTime: int(startTime),
				VideoURL:      videoURL,
				FileID:        video.FileID,
			}
		}

		err = s.requestNoRealTimeDataProcessing(reqType, jobRecord.CompanyID, jobRecord.ID, jobRecord.WaylineJobID, jobRecord.AlgorithmTarget, reqData)
		if err != nil {
			s.logger.Debugf("handleNoRealTimeJobData: request no real-time job data processing failed, jobRecordID: %d, err: %v", jobRecord.ID, err)
			return
		}
	}
}

// HandleRealTimeDataResp 处理返回的实时数据
func (s *JobDataProcessorService) HandleRealTimeDataResp(isSucceeded bool, data dto.RealTimeResp) {
	connId := fmt.Sprintf("%s-%s", fileds.ServerRealTime, data.DockSn)
	lockKey := fmt.Sprintf(fileds.RealTimeJobDataLock, connId)

	jobID := data.JobID

	if !isSucceeded {
		// 释放锁
		if err := s.redis.Del(lockKey).Err(); err != nil {
			s.logger.Errorf("HandleRealTimeDataResp: del lock key: %s, err: %v", lockKey, err)
			return
		}

		waylineJob, err := NewWaylineJobService().GetWaylineJobByJobId(jobID)
		if err != nil {
			s.logger.Errorf("HandleRealTimeDataResp: get waylineJob failed, jobId: %s, err: %v", jobID, err)
			return
		}

		err = NewLiveStreamRedisService().delLiveStreamingProcessing(waylineJob.DockSN)
		if err != nil {
			s.logger.Errorf("HandleRealTimeDataResp: delLiveStreamingProcessing failed, dockSn: %s, err: %v", waylineJob.DockSN, err)
			return
		}
		s.logger.Warnf("HandleRealTimeDataResp: predict server handle data failed, del lock key: %s", lockKey)
		return
	}

	// 将实时告警返回给前端
	wsManager := repo.NewWsManager()

	bid := data.DroneSn
	serviceKey := fileds.RealTimeAlarmInfo

	if wsManager.ServiceSessionsMap[serviceKey] == nil || wsManager.ServiceSessionsMap[serviceKey][bid] == nil {
		return
	}

	drone, err := NewDeviceService().GetDeviceWithCompanyBySn(data.DroneSn)
	if err != nil {
		return
	}

	var typeDesc []string
	for _, objectType := range data.ObjectTypeList {
		warnType := enum.FindJobWarnTypeEnum(objectType)

		if warnType == enum.JobWarnTypeEnumUnknown {
			typeDesc = append(typeDesc, objectType)
		} else {
			typeDesc = append(typeDesc, warnType.String())
		}
	}

	// 创建告警记录，将实时告警添加到数据库
	// 获取任务记录
	jobRecord, err := NewInspectionJobRecordService().getByWaylineJobId(jobID)
	if err != nil {
		s.logger.Errorf("HandleRealTimeDataResp: getByWaylineJobId failed, jobId: %s, err: %v", jobID, err)
		return
	}

	// 获取无人机信息
	droneOsd, err := s.deviceRedisService.getDockDroneOsd(data.DroneSn)
	if err != nil {
		s.logger.Errorf("HandleRealTimeDataResp: getDockDroneOsd failed, droneSn: %s, err: %v", data.DroneSn, err)
		return
	}

	var lng, lat float64
	if droneOsd != nil {
		lng = *droneOsd.Longitude
		lat = *droneOsd.Latitude
	}

	level := 1
	areas, ok := fileds.ProtectedAreas[jobRecord.CompanyID]
	if ok {
		result := CheckPointInProtectedArea(areas, lng, lat)
		if result == nil {
			// 如果不在保护区内，就设置为3
			level = 3
		} else {
			level = *result
		}
	}
	var picListByte, warnTypeByte, positionByte []byte
	picList := []string{data.PicObjectKey}
	picListByte, err = json.Marshal(picList)
	if err != nil {
		s.logger.Errorf("HandleRealTimeDataResp: marshal picList failed, err: %v", err)
		return
	}

	var warnType []string
	// 将req.WarnDesc通过查找JobWarnTypeEnum，将req.WarnDesc转成warnType，例如["漂浮物"]，根据类型定义就转成["piaofu"]
	for _, desc := range typeDesc {
		enumValue := enum.FindJobWarnTypeEnumByString(desc)
		if enumValue != enum.JobWarnTypeEnumUnknown {
			warnType = append(warnType, enumValue.Value())
		} else {
			// 如果找不到对应的枚举值，保留原始描述
			warnType = append(warnType, desc)
		}
	}
	warnTypeByte, err = json.Marshal(warnType)
	if err != nil {
		s.logger.Errorf("HandleRealTimeDataResp: marshal warnType failed, err: %v", err)
		return
	}

	baiduLongitude, baiduLatitude, err := utils.CoordinateTransformV2(lng, lat, fileds.StandardToBd09ll)
	if err != nil {
		s.logger.Errorf("HandleRealTimeDataResp: coordinate transform failed, lng: %f, lat: %f, err: %v", lng, lat, err)
		return
	}

	position := dto.JobWarnRecordPosition{
		Bd09ll: request.Position{
			Lat: baiduLatitude,
			Lng: baiduLongitude,
		},
		Standard: request.Position{
			Lat: lat,
			Lng: lng,
		},
	}

	positionByte, err = json.Marshal(position)
	if err != nil {
		s.logger.Errorf("HandleRealTimeDataResp: marshal position failed, err: %v", err)
		return
	}

	warnDesc := strings.Join(typeDesc, ",")

	sec := int64(data.CurrentSec)                         // 获取整数秒部分
	nsec := int64((data.CurrentSec - float64(sec)) * 1e9) // 获取纳秒部分

	jobWarnRecord := model.JobWarnRecord{
		CompanyID:     jobRecord.CompanyID,
		JobRecordID:   jobRecord.ID,
		JobRecordName: jobRecord.Name,
		WarnDesc:      warnDesc,
		PicList:       string(picListByte),
		WarnType:      string(warnTypeByte),
		Position:      string(positionByte),
		Level:         level,
		Status:        2,
		WarnAt:        dbx.TimeStamp{Time: time.Unix(sec, nsec)},
		DroneSN:       data.DroneSn,
	}

	if err = s.db.Create(&jobWarnRecord).Error; err != nil {
		s.logger.Errorf("HandleRealTimeDataResp: create jobWarnRecord failed, err: %v", err)
		return
	}

	if err = s.CompactAlarms(&jobWarnRecord, jobRecord.ID, 300.0); err != nil {
		s.logger.Errorf("HandleRealTimeDataResp: CompactAlarms failed, err: %v", err)
		return
	}

	// 包含 91 或 94，继续执行
	if funk.ContainsInt(drone.CompanyID, 91) || funk.ContainsInt(drone.CompanyID, 94) {
		respData := map[string]interface{}{
			"type_desc":   typeDesc,
			"current_sec": data.CurrentSec,
		}

		resp := make(map[string]map[string]interface{})
		resp[serviceKey] = respData

		encodedData, err := json.Marshal(resp)
		if err != nil {
			return
		}

		// fixme 临时解决代码，为了避免画面和通知不同步
		if funk.ContainsInt(drone.CompanyID, 94) {
			time.Sleep(3 * time.Second)
		} else {
			time.Sleep(3 * time.Second)
		}

		wsManager.BroadCastData(encodedData, serviceKey, bid, "")
	}

	// 包含 94 或 91，发送短信
	if funk.ContainsInt(drone.CompanyID, 94) || funk.ContainsInt(drone.CompanyID, 91) {
		if jobRecord.PersonNumber != "" {
			conf := config.GetConfig()
			// 创建短信客户端
			config := &client.SMSConfig{
				ApId:      conf.Sms.ApId,
				SecretKey: conf.Sms.SecretKey,
				EcName:    conf.Sms.EcName,
				Sign:      conf.Sms.Sign,
				AddSerial: conf.Sms.AddSerial,
				URL:       conf.Sms.Url,
			}
			smsClient := client.NewSMSClient(config)

			formattedTime := time.Unix(int64(data.CurrentSec), 0).Format("15:04:05")

			var link string
			var content string

			if funk.ContainsInt(drone.CompanyID, 94) {
				link = fmt.Sprintf("http://xm.boeing.meitengtech.com/static/warn-notice.html?type=%s&id=%d", "warn_record", jobWarnRecord.ID)
				content = fmt.Sprintf("【厦门轨道智慧地保警告】\n厦门地铁四号线地保警报：系统于%s发现%s，请点击链接查看告警内容与告警位置\n%s", formattedTime, strings.Join(typeDesc, "、"), link)
			}

			if funk.ContainsInt(drone.CompanyID, 91) {
				link = fmt.Sprintf("http://test.boeing.meitengtech.com/static/warn-notice.html?type=%s&id=%d", "warn_record", jobWarnRecord.ID)
				content = fmt.Sprintf("【福州轨道智慧地保警告】\n福州地铁地保警报：系统于%s发现%s，请点击链接查看告警内容与告警位置\n%s", formattedTime, strings.Join(typeDesc, "、"), link)
			}

			// 获取详细发送结果
			_, err = smsClient.GetSendResult(jobRecord.PersonNumber, content, false)
			if err != nil {
				s.logger.Errorf("HandleRealTimeDataResp: send SMS failed, phone: %s, err: %v", jobRecord.PersonNumber, err)
				return
			}
		}
	}

}

// requestNoRealTimeDataProcessing 向算法服务请求处理非实时数据
func (s *JobDataProcessorService) requestNoRealTimeDataProcessing(reqType string, cid int, jobRecordID int, waylineJobID string, jobTargetClasses string, data interface{}) error {
	var targetClasses []string
	if jobTargetClasses != "" {
		if err := json.Unmarshal([]byte(jobTargetClasses), &targetClasses); err != nil {
			s.logger.Errorf("requestNoRealTimeDataProcessing: failed to decode AlgorithmTarget, err: %v", err)
			return err
		}
	} else {
		// fixme 兼容老数据，后续可以去掉
		company := model.Company{}
		err := s.db.Model(model.Company{}).Where(model.Company{ID: cid}).First(&company).Error
		if err != nil {
			s.logger.Errorf("requestNoRealTimeDataProcessing: failed to get company, cid: %d, err: %v", cid, err)
			return err
		}
		if company.AlgorithmTarget != "" {
			if err = json.Unmarshal([]byte(company.AlgorithmTarget), &targetClasses); err != nil {
				s.logger.Errorf("requestNoRealTimeDataProcessing: failed to decode AlgorithmTarget, err: %v", err)
				return err
			}
		}
	}

	uploadPathFunc, ok := UploadTypeMapping[TaskRawResult]
	if !ok {
		return fmt.Errorf("uploadType:%s is undefined", TaskRawResult)
	}

	path, err := uploadPathFunc(nil, cid, "", ExtInfo{JobId: waylineJobID})
	if err != nil {
		return err
	}

	req := dto.JobDataProcessingReq{
		Type: reqType,
		Data: dto.StartNoRealTimeReq{
			JobRecordID:     jobRecordID,
			TargetClasses:   targetClasses,
			ObjectKeyPrefix: path,
			AttitudeKey:     fmt.Sprintf(fileds.DroneAttitudeInfo, waylineJobID),
			Data:            data,
		},
	}

	var bytes []byte
	if bytes, err = json.Marshal(&req); err != nil {
		s.logger.Errorf("requestNoRealTimeDataProcessing: failed to encode data, err: %v", err)
		return err
	}

	connID := fileds.ServerNoRealTime
	wsManager := repo.NewWsManager()

	// 检查websocket连接
	if _, ok = wsManager.WsConn[connID]; !ok {
		err = fmt.Errorf("requestNoRealTimeDataProcessing: failed to get websocket connection, or connection closed. connID: %s", connID)
		s.logger.Debugf(err.Error())
		return nil
	}

	// 上锁
	lockKey := fmt.Sprintf(fileds.NoRealTimeJobDataLock, strconv.Itoa(jobRecordID))

	var acquired bool
	acquired, err = s.redis.SetNX(lockKey, "locked", fileds.NoRealTimeLockTimeout).Result()
	if err != nil {
		s.logger.Errorf("requestNoRealTimeDataProcessing: failed to lock. lockKey: %s, err: %v", lockKey, err)
		return err
	}
	if !acquired {
		err = fmt.Errorf("requestNoRealTimeDataProcessing: failed to lock. lockKey: %s", lockKey)
		s.logger.Errorf(err.Error())
		return err
	}

	s.logger.Infof(string(bytes))

	// 发送请求
	wsManager.SendData(bytes, connID)

	return nil
}

// HandleNoRealTimeVideoDataResp 处理返回的非实时视频数据
func (s *JobDataProcessorService) HandleNoRealTimeVideoDataResp(isSucceeded bool, data dto.NoRealTimeVideoResp) {
	s.logger.Infof("HandleNoRealTimeDataResp: jobRecordID: %d,isSucceeded: %v", data.JobRecordID, isSucceeded)
	s.logger.Infof("HandleNoRealTimeDataResp: data: %+v", data)

	jobRecordID := data.JobRecordID
	fileID := data.FileID
	lockKey := fmt.Sprintf(fileds.NoRealTimeJobDataLock, strconv.Itoa(jobRecordID))

	// 释放锁
	defer func() {
		if err := s.redis.Del(lockKey).Err(); err != nil {
			s.logger.Errorf("HandleNoRealTimeDataResp: failed to del lock key: %s, err: %v", lockKey, err)
			return
		}
	}()

	if !isSucceeded {
		s.logger.Warnf("HandleNoRealTimeDataResp: predict server handle data failed, del lock key: %s", lockKey)
		return
	}

	var mediaFile model.MediaFile
	err := s.db.Model(&model.MediaFile{}).Where("file_id", fileID).First(&mediaFile).Error
	if err != nil {
		s.logger.Errorf("HandleNoRealTimeDataResp: failed to get media file, fileID: %s, err: %v", fileID, err)
		return
	}

	// 告警忽略区
	fenceList := make([]model.Fence, 0)
	err = s.db.Model(model.Fence{}).Where("is_deleted = ?", false).Where("company_id", mediaFile.CompanyID).Find(&fenceList).Error
	if err != nil {
		s.logger.Errorf("HandleNoRealTimeDataResp: failed to get fence, err: %v", err)
		return
	}

	jobRecord, err := NewInspectionJobRecordService().GetById(jobRecordID)
	if err != nil {
		s.logger.Errorf("HandleNoRealTimeDataResp: failed to get job record, waylineJobID: %s, err: %v", mediaFile.WaylineJobID, err)
		return
	}

	var points []dto.KMLPoint
	err = json.Unmarshal([]byte(jobRecord.TargetTrajectory), &points)
	if err != nil {
		s.logger.Errorf("HandleNoRealTimeDataResp: failed to decode target trajectory, err: %v", err)
		return
	}

	var trajectory []dto.Trajectory
	if err = json.Unmarshal([]byte(jobRecord.TaskTrajectory), &trajectory); err != nil {
		s.logger.Errorf("HandleNoRealTimeDataResp: failed to decode trajectory, err: %v", err)
		return
	}

	// 告警记录
	jobWarnRecordDtoList, err := genAlarmRecordsWithVideo(data.Alarm, *jobRecord, fenceList, mediaFile.Drone, mediaFile.RecordStartTime)
	if err != nil {
		s.logger.Errorf("HandleNoRealTimeDataResp: failed to create warn record, err: %v", err)
		return
	}

	jobWarnRecordList := make([]model.JobWarnRecord, 0)

	for _, warnRecord := range jobWarnRecordDtoList {
		var warnTypeListByte []byte
		if warnTypeListByte, err = json.Marshal(warnRecord.WarnType); err != nil {
			s.logger.Errorf("HandleNoRealTimeDataResp: failed to marshal object type list, err: %v", err)
			continue
		}

		var picList []byte
		if picList, err = json.Marshal(warnRecord.PicObjectKeyList); err != nil {
			s.logger.Errorf("HandleNoRealTimeDataResp: failed to marshal pic list, err: %v", err)
			continue
		}

		var positionJson []byte
		if positionJson, err = json.Marshal(warnRecord.Position); err != nil {
			s.logger.Errorf("HandleNoRealTimeDataResp: failed to marshal position, err: %v", err)
			continue
		}

		jobWarnRecord := model.JobWarnRecord{
			CompanyID:       warnRecord.CompanyID,
			JobRecordID:     warnRecord.JobRecordID,
			JobRecordName:   warnRecord.JobRecordName,
			WarnDesc:        warnRecord.WarnDesc,
			PicList:         string(picList),
			Position:        string(positionJson),
			Level:           warnRecord.Level,
			WarnType:        string(warnTypeListByte),
			Status:          warnRecord.Status,
			WarnAt:          warnRecord.WarnAt,
			DroneSN:         warnRecord.DroneSN,
			AlgorithmSource: warnRecord.AlgorithmSource,
			ModelCheck:      warnRecord.ModelCheck,
		}

		jobWarnRecordList = append(jobWarnRecordList, jobWarnRecord)
	}

	// 目前只让这个公司生成报告
	var docxObjectKey string
	if funk.Contains(reportCids, jobRecord.CompanyID) {
		docxObjectKey, err = getReport(*jobRecord, jobWarnRecordDtoList, points)
		if err != nil {
			s.logger.Errorf("HandleNoRealTimeDataResp: failed to get report, err: %v", err)
			return
		}
	}

	alarmInfo, err := json.Marshal(data.Alarm)
	if err != nil {
		s.logger.Errorf("HandleNoRealTimeDataResp: failed to encode alarm info, err: %v", err)
		return
	}

	warnVideo := model.WarnVideo{
		CompanyID:      mediaFile.CompanyID,
		JobRecordID:    jobRecord.ID,
		OriginalFileID: fileID,
		FileName:       filepath.Base(data.VideoObjectKey),
		ObjectKey:      data.VideoObjectKey,
		AlarmInfo:      string(alarmInfo),
	}

	tx := s.db.Begin()

	if err = tx.Create(&warnVideo).Error; err != nil {
		s.logger.Errorf("HandleNoRealTimeDataResp: failed to create warn video, err: %v", err)
		tx.Rollback()
		return
	}

	columns := map[string]interface{}{
		"report_docx_object_key": docxObjectKey,
		"processed_status":       enum.JobProcessedStatusEnum_Processed.Value(),
	}

	err = tx.Model(model.InspectionJobRecord{}).Where("id = ?", jobRecord.ID).Updates(columns).Error
	if err != nil {
		s.logger.Errorf("HandleNoRealTimeDataResp: failed to set report_object_key, err: %v", err)
		tx.Rollback()
		return
	}

	// 创建告警记录
	if len(jobWarnRecordList) > 0 {
		if err = tx.Create(&jobWarnRecordList).Error; err != nil {
			s.logger.Errorf("HandleNoRealTimeDataResp: failed to create new warn records, err: %v", err)
			tx.Rollback()
			return
		}
	}

	tx.Commit()

	// 告警聚合
	for _, jobWarnRecord := range jobWarnRecordList {
		s.CompactAlarms(&jobWarnRecord, jobRecord.ID, 300.0)
	}

	// todo 告警推送
	// 公司ID为 94 或 91，发送短信
	if jobRecord.CompanyID == 94 || jobRecord.CompanyID == 91 {
		s.sendWarningSMS(jobRecord, jobRecordID, jobRecord.CompanyID)
	}

	s.logger.Debugf("handleNoRealTimeJobData: file_id: %s, done", fileID)
}

// HandleNoRealTimePicDataResp 处理返回的非实时图片数据
func (s *JobDataProcessorService) HandleNoRealTimePicDataResp(isSucceeded bool, data dto.NoRealTimePicResp) {
	s.logger.Infof("HandleNoRealTimePicDataResp: jobRecordID: %d,isSucceeded: %v", data.JobRecordID, isSucceeded)

	jobRecordID := data.JobRecordID
	lockKey := fmt.Sprintf(fileds.NoRealTimeJobDataLock, strconv.Itoa(jobRecordID))

	// 释放锁
	defer func() {
		if err := s.redis.Del(lockKey).Err(); err != nil {
			s.logger.Errorf("HandleNoRealTimePicDataResp: failed to del lock key: %s, err: %v", lockKey, err)
			return
		}
	}()

	if !isSucceeded {
		s.logger.Warnf("HandleNoRealTimePicDataResp: predict server handle data failed, del lock key: %s", lockKey)
		return
	}

	jobRecord, err := NewInspectionJobRecordService().GetById(jobRecordID)
	if err != nil {
		s.logger.Errorf("HandleNoRealTimePicDataResp: failed to get job record, err: %v", err)
		return
	}

	var points []dto.KMLPoint
	err = json.Unmarshal([]byte(jobRecord.TargetTrajectory), &points)
	if err != nil {
		s.logger.Errorf("HandleNoRealTimePicDataResp: failed to decode target trajectory, err: %v", err)
		return
	}

	var trajectory []dto.Trajectory
	if err = json.Unmarshal([]byte(jobRecord.TaskTrajectory), &trajectory); err != nil {
		s.logger.Errorf("HandleNoRealTimePicDataResp: failed to decode trajectory, err: %v", err)
		return
	}

	waylineJob, err := NewWaylineJobService().GetWaylineJobByJobId(jobRecord.WaylineJobID)
	if err != nil {
		s.logger.Errorf("HandleNoRealTimePicDataResp: failed to get wayline job, err: %v", err)
		return
	}

	// 告警记录
	jobWarnRecordDtoList, err := genAlarmRecordsWithPic(data.DataList, *jobRecord, waylineJob.DroneSN)
	if err != nil {
		s.logger.Errorf("HandleNoRealTimePicDataResp: failed to create warn record, err: %v", err)
		return
	}

	jobWarnRecordList := make([]model.JobWarnRecord, 0)

	for _, warnRecord := range jobWarnRecordDtoList {
		var warnTypeListByte []byte
		if warnTypeListByte, err = json.Marshal(warnRecord.WarnType); err != nil {
			s.logger.Errorf("HandleNoRealTimePicDataResp: failed to marshal object type list, err: %v", err)
			continue
		}

		var picList []byte
		if picList, err = json.Marshal(warnRecord.PicObjectKeyList); err != nil {
			s.logger.Errorf("HandleNoRealTimePicDataResp: failed to marshal pic list, err: %v", err)
			continue
		}

		var positionJson []byte
		if positionJson, err = json.Marshal(warnRecord.Position); err != nil {
			s.logger.Errorf("HandleNoRealTimePicDataResp: failed to marshal position, err: %v", err)
			continue
		}

		jobWarnRecord := model.JobWarnRecord{
			CompanyID:     warnRecord.CompanyID,
			JobRecordID:   warnRecord.JobRecordID,
			JobRecordName: warnRecord.JobRecordName,
			WarnDesc:      warnRecord.WarnDesc,
			PicList:       string(picList),
			Position:      string(positionJson),
			Level:         warnRecord.Level,
			WarnType:      string(warnTypeListByte),
			Status:        warnRecord.Status,
			WarnAt:        warnRecord.WarnAt,
			DroneSN:       warnRecord.DroneSN,
		}

		jobWarnRecordList = append(jobWarnRecordList, jobWarnRecord)
	}

	var docxObjectKey string
	if funk.Contains(reportCids, jobRecord.CompanyID) {
		docxObjectKey, err = getReport(*jobRecord, jobWarnRecordDtoList, points)
		if err != nil {
			s.logger.Errorf("HandleNoRealTimePicDataResp: failed to get report, err: %v", err)
			return
		}
	}

	tx := s.db.Begin()

	// 修改jobRecord的processed_status和report_docx_object_key
	columns := map[string]interface{}{
		"report_docx_object_key": docxObjectKey,
		"processed_status":       enum.JobProcessedStatusEnum_Processed.Value(),
	}

	err = tx.Model(model.InspectionJobRecord{}).Where("id = ?", jobRecord.ID).Updates(columns).Error
	if err != nil {
		s.logger.Errorf("HandleNoRealTimePicDataResp: failed to set report_object_key, err: %v", err)
		tx.Rollback()
		return
	}

	// 再创建告警记录
	if len(jobWarnRecordList) > 0 {
		if err = tx.Create(&jobWarnRecordList).Error; err != nil {
			s.logger.Errorf("HandleNoRealTimeDataResp: failed to create new warn records, err: %v", err)
			tx.Rollback()
			return
		}
	}

	tx.Commit()

	// 告警聚合
	for _, jobWarnRecord := range jobWarnRecordList {
		s.CompactAlarms(&jobWarnRecord, jobRecord.ID, 300.0)
	}

	// todo 告警推送
	// 公司ID为 94 或 91，发送短信
	if jobRecord.CompanyID == 94 || jobRecord.CompanyID == 91 {
		s.sendWarningSMS(jobRecord, jobRecordID, jobRecord.CompanyID)
	}

	s.logger.Debugf("HandleNoRealTimePicDataResp: record_id: %d, done", jobRecordID)
}

// aggregateJobWarnRecordsByPosition 根据告警级别和地理位置聚合告警记录
func (s *JobDataProcessorService) aggregateJobWarnRecordsByPosition(jobWarnRecordDtoList []dto.JobWarnRecordDto, firstReferencePoint request.Position, aggregationDistance float64) [][]dto.JobWarnRecordDto {
	// 按告警级别分组
	levelGroups := make(map[int][]dto.JobWarnRecordDto)
	for _, record := range jobWarnRecordDtoList {
		levelGroups[record.Level] = append(levelGroups[record.Level], record)
	}

	// 存储最终结果
	allGroupsByPosition := make([][]dto.JobWarnRecordDto, 0)

	// 对每个告警级别内的记录进行地理位置聚合
	for _, recordsInLevel := range levelGroups {
		if len(recordsInLevel) == 0 {
			continue
		}

		// 对当前级别内的记录按地理位置聚合
		groupsByPosition := s.aggregateRecordsByPosition(recordsInLevel, firstReferencePoint, aggregationDistance)

		// 将当前级别的聚合结果添加到总结果中
		allGroupsByPosition = append(allGroupsByPosition, groupsByPosition...)
	}

	return allGroupsByPosition
}

// aggregateRecordsByPosition 按地理位置聚合告警记录（同一级别内）
func (s *JobDataProcessorService) aggregateRecordsByPosition(recordsInLevel []dto.JobWarnRecordDto, firstReferencePoint request.Position, aggregationDistance float64) [][]dto.JobWarnRecordDto {
	// 以地理范围聚合告警
	warnRecordsGroupByPosition := make([][]dto.JobWarnRecordDto, 0)
	// 已聚合的告警ID集合
	aggregatedRecordIDs := make(map[int]bool)

	// 当前使用的参考点
	currentReferencePoint := firstReferencePoint

	// 循环直到所有告警记录都被聚合
	for len(aggregatedRecordIDs) < len(recordsInLevel) {
		// 找到距离当前参考点最近的未聚合记录
		var closestRecordIndex = -1
		var minDistance float64 = math.MaxFloat64

		for i := 0; i < len(recordsInLevel); i++ {
			// 跳过已聚合的记录
			if aggregatedRecordIDs[i] {
				continue
			}

			// 获取记录的位置信息
			recordPosition := recordsInLevel[i].Position

			// 计算与参考点的距离
			dis := utils.GetDistanceInMeter(
				currentReferencePoint.Lat,
				currentReferencePoint.Lng,
				recordPosition.Standard.Lat,
				recordPosition.Standard.Lng,
			)

			// 如果这是距离参考点最近的记录
			if dis < minDistance {
				minDistance = dis
				closestRecordIndex = i
			}
		}

		// 如果没有找到最近的记录（可能所有记录都已聚合），则退出循环
		if closestRecordIndex == -1 {
			break
		}

		// 创建新的聚合组，并添加最近的记录
		currentGroup := []dto.JobWarnRecordDto{recordsInLevel[closestRecordIndex]}
		aggregatedRecordIDs[closestRecordIndex] = true

		// 获取当前记录的位置信息
		centerPosition := recordsInLevel[closestRecordIndex].Position

		// 查找距离聚合中心点指定范围内的其他记录
		for j := 0; j < len(recordsInLevel); j++ {
			// 跳过已聚合的记录和中心点自身
			if aggregatedRecordIDs[j] || j == closestRecordIndex {
				continue
			}

			// 获取待比较记录的位置信息
			comparePosition := recordsInLevel[j].Position

			// 计算两点之间的距离
			dis := utils.GetDistanceInMeter(
				centerPosition.Standard.Lat,
				centerPosition.Standard.Lng,
				comparePosition.Standard.Lat,
				comparePosition.Standard.Lng,
			)

			// 如果距离小于等于指定距离，则加入当前聚合组
			if dis <= aggregationDistance {
				currentGroup = append(currentGroup, recordsInLevel[j])
				aggregatedRecordIDs[j] = true
			}
		}

		// 对当前组内的记录按WarnAt字段升序排序
		sort.Slice(currentGroup, func(i, j int) bool {
			return currentGroup[i].WarnAt.Before(currentGroup[j].WarnAt.Time)
		})

		// 将当前聚合组添加到结果中
		warnRecordsGroupByPosition = append(warnRecordsGroupByPosition, currentGroup)

		// 更新参考点为当前聚合中心点的位置，为下一轮聚合做准备
		currentReferencePoint = centerPosition.Standard
	}

	return warnRecordsGroupByPosition
}

// genAlarmRecordsWithVideo 基于视频告警结果生成告警记录
func genAlarmRecordsWithVideo(alarmInfo []dto.AlarmDto, jobRecord model.InspectionJobRecord, fenceList []model.Fence, droneSn string, recordStartTime *time.Time) ([]dto.JobWarnRecordDto, error) {
	cid := jobRecord.CompanyID

	var trajectory []dto.Trajectory
	err := json.Unmarshal([]byte(jobRecord.TaskTrajectory), &trajectory)
	if err != nil {
		repo.GetLogger().Error("decode trajectory error:", err)
		return nil, err
	}

	warnRecordList := make([]dto.JobWarnRecordDto, 0)

	var startTime float64
	// fixme 以下判断是为了兼容老数据
	if recordStartTime != nil && !recordStartTime.IsZero() {
		startTime = float64(recordStartTime.Unix())
	} else if !jobRecord.LiveStartTime.IsZero() {
		startTime = float64(jobRecord.LiveStartTime.Unix())
	} else if !jobRecord.ExecuteTime.IsZero() {
		startTime = float64(jobRecord.ExecuteTime.Unix())
	}

	for _, alarmDto := range alarmInfo {
		// 根据进度时间和开始时间推算绝对时间和坐标
		alarmTime := startTime + alarmDto.TimeSec
		closest := findClosestPosition(trajectory, alarmTime)

		// 检查忽略区
		isIgnored := false

		for _, fence := range fenceList {
			var coordinates []utils.Coordinate
			if coordinates, err = utils.ParseCoordinates(fence.Polygons); err != nil {
				repo.GetLogger().Errorf("genAlarmRecordsWithVideo: failed to parse fence polygons, fenceID: %d, err: %v", fence.ID, err)
				continue
			}

			valid := utils.Coordinate{Lng: closest.Bd09ll.Lng, Lat: closest.Bd09ll.Lat}
			if utils.IsPointInRectangle(valid, coordinates) {
				repo.GetLogger().Debugf("genAlarmRecordsWithVideo: ignore alarm, timeSec: %f.6, fenceID: %d", alarmDto.TimeSec, fence.ID)
				isIgnored = true
			}
		}

		if isIgnored {
			continue
		}

		var typeDesc []string
		for _, objectType := range alarmDto.ObjectTypeList {
			warnType := enum.FindJobWarnTypeEnum(objectType)

			if warnType == enum.JobWarnTypeEnumUnknown {
				typeDesc = append(typeDesc, objectType)
			} else {
				typeDesc = append(typeDesc, warnType.String())
			}
		}

		position := dto.JobWarnRecordPosition{
			Bd09ll:   closest.Bd09ll,
			Standard: closest.Standard,
		}

		// 根据区域级别来确定告警级别
		// 获取公司对应的区域，没有的话就默认最高级别
		level := 1
		areas, ok := fileds.ProtectedAreas[jobRecord.CompanyID]
		if ok {
			result := CheckPointInProtectedArea(areas, closest.Standard.Lng, closest.Standard.Lat)
			if result == nil {
				// 如果不在保护区内，就设置为3
				level = 3
			} else {
				level = *result
			}
		}

		warnRecordDto := dto.JobWarnRecordDto{
			CompanyID:        cid,
			JobRecordID:      jobRecord.ID,
			JobRecordName:    jobRecord.Name,
			WarnDesc:         strings.Join(typeDesc, ","),
			PicObjectKeyList: []string{alarmDto.PicObjectKey},
			Position:         position,
			Level:            level,
			WarnType:         alarmDto.ObjectTypeList,
			Status:           2,
			WarnAt:           dbx.TimeStamp{Time: time.Unix(int64(alarmTime), 0)},
			DroneSN:          droneSn,
			AlgorithmSource:  alarmDto.AlgorithmSource,
			ModelCheck:       alarmDto.ModelCheck,
		}

		warnRecordList = append(warnRecordList, warnRecordDto)
	}

	return warnRecordList, nil
}

// genAlarmRecordsWithPic 基于图片告警结果生成告警记录
func genAlarmRecordsWithPic(dataList []dto.PicDataResp, jobRecord model.InspectionJobRecord, droneSn string) ([]dto.JobWarnRecordDto, error) {
	cid := jobRecord.CompanyID

	warnRecordList := make([]dto.JobWarnRecordDto, 0)

	for _, data := range dataList {
		var typeDesc []string
		for _, objectType := range data.ObjectTypeList {
			warnType := enum.FindJobWarnTypeEnum(objectType)

			if warnType == enum.JobWarnTypeEnumUnknown {
				typeDesc = append(typeDesc, objectType)
			} else {
				typeDesc = append(typeDesc, warnType.String())
			}
		}

		baiduLongitude, baiduLatitude, err := utils.CoordinateTransformV2(data.ExifData.LRFTargetLon, data.ExifData.LRFTargetLat, fileds.StandardToBd09ll)
		if err != nil {
			repo.GetLogger().Errorf("genAlarmRecordsWithPic: transform geocoding err: %v", err)
		}

		position := dto.JobWarnRecordPosition{
			Bd09ll: request.Position{
				Lat: baiduLatitude,
				Lng: baiduLongitude,
			},
			Standard: request.Position{
				Lat: data.ExifData.LRFTargetLat,
				Lng: data.ExifData.LRFTargetLon,
			},
		}

		// 根据区域级别来确定告警级别
		// 获取公司对应的区域，没有的话就默认最高级别
		level := 1
		areas, ok := fileds.ProtectedAreas[jobRecord.CompanyID]
		if ok {
			result := CheckPointInProtectedArea(areas, data.ExifData.LRFTargetLon, data.ExifData.LRFTargetLat)
			if result == nil {
				// 如果不在保护区内，就设置为3
				level = 3
			} else {
				level = *result
			}
		}

		warnRecordDto := dto.JobWarnRecordDto{
			CompanyID:        cid,
			JobRecordID:      jobRecord.ID,
			JobRecordName:    jobRecord.Name,
			WarnDesc:         strings.Join(typeDesc, ","),
			PicObjectKeyList: []string{data.OutputObjectKey},
			Position:         position,
			WarnType:         data.ObjectTypeList,
			Status:           2,
			Level:            level,
			WarnAt:           dbx.TimeStamp{Time: time.Unix(int64(data.ExifData.DateTimeOriginal), 0)},
			DroneSN:          droneSn,
		}

		warnRecordList = append(warnRecordList, warnRecordDto)
	}

	return warnRecordList, nil
}

// getReport 生成巡检报告
func getReport(jobRecord model.InspectionJobRecord, jobWarnRecordDtoList []dto.JobWarnRecordDto, points []dto.KMLPoint) (string, error) {
	var kmlFlightParams dto.KMLFlightParams
	if jobRecord.KMLFlightParams != "" {
		if err := json.Unmarshal([]byte(jobRecord.KMLFlightParams), &kmlFlightParams); err != nil {
			return "", err
		}
	}

	var lostAction, mode, finishAction string
	switch kmlFlightParams.ExecuteRCLostAction {
	case "goBack":
		lostAction = "返航"
	case "landing":
		lostAction = "降落"
	case "hover":
		lostAction = "悬停"
	}

	switch kmlFlightParams.GlobalWaypointTurnMode {
	case "coordinateTurn":
		mode = "协调转弯"
	case "toPointAndStopWithDiscontinuityCurvature":
		mode = "直线飞行"
	case "toPointAndStopWithContinuityCurvature":
		mode = "曲线飞行，飞行器到点停"
	case "toPointAndPassWithContinuityCurvature":
		mode = "曲线飞行，飞行器过点不停"
	}

	switch kmlFlightParams.FinishAction {
	case "goHome":
		finishAction = "直线返航"
	case "noAction":
		finishAction = "无"
	case "autoLand":
		finishAction = "原地降落"
	case "gotoFirstWaypoint":
		finishAction = "飞向航线起始点"
	}

	warnRecordList := make([]map[string]interface{}, 0)
	typeCountMap := make(map[string]int)
	ossSvc := NewOssService()

	for _, record := range jobWarnRecordDtoList {
		// 巡检分析表格

		// fixme 后续一个记录可能有多个图片
		pic := record.PicObjectKeyList[0]
		picUrl, err := ossSvc.GetObjectSignURL(pic, int64(60*60*24))
		if err != nil {
			return "", err
		}

		warnRecordList = append(warnRecordList, map[string]interface{}{
			"name":   fmt.Sprintf("%s区间", jobRecord.WaylineName),
			"mode":   "自动模式",
			"time":   record.WarnAt.Time.Format("2006-1-2"),
			"status": "异常",
			"pic":    picUrl,
			"type":   record.WarnDesc,
		})

		// 告警类型统计
		for _, warnType := range record.WarnType {
			typeCountMap[warnType]++
		}
	}

	var warnTotal int
	var warnDesc string
	for warnType, count := range typeCountMap {
		warnDesc = warnDesc + fmt.Sprintf("%d处%s, ", count, enum.FindJobWarnTypeEnum(warnType).String())
		warnTotal += count
	}

	if len(warnDesc) > 2 {
		warnDesc = warnDesc[:len(warnDesc)-2]
	}

	rand.Seed(time.Now().UnixNano())
	minNum := 25
	maxNum := 35
	random := rand.Intn(maxNum-minNum+1) + minNum

	// height 不同航线高度模式有不同的取值 海拔高度/相对起飞点高度/相对地形高度
	var gimbalPitchAngle, height, alt, asl float64
	var area, areaTopAsl, areaBottomAsl float64
	var docxTemplateName string

	// fixme 暂时用固定值
	if jobRecord.CompanyID == 88 {
		docxTemplateName = "report_template_tianjin.docx"
		gimbalPitchAngle = -90
		height = kmlFlightParams.TakeOffSecurityHeight
		alt = kmlFlightParams.TakeOffSecurityHeight
		asl = kmlFlightParams.TakeOffSecurityHeight + 2
	} else if jobRecord.CompanyID == 90 {
	} else if jobRecord.CompanyID == 91 || jobRecord.CompanyID == 1 {
		if jobRecord.CompanyID == 91 {
			docxTemplateName = "report_template_fuzhou.docx"
		} else if jobRecord.CompanyID == 1 {
			docxTemplateName = "report_template_none.docx"
		}

		waylineManage, err := NewWaylineManageService().GetWaylineManageById(jobRecord.WaylineManageID)
		if err != nil {
			return "", fmt.Errorf("waylineManage: %d, err: %v", jobRecord.WaylineManageID, err)
		}

		if waylineManage.WaylineFileID == 292 || waylineManage.WaylineFileID == 306 || waylineManage.WaylineFileID == 314 {
			// 地铁地保巡查航线
			gimbalPitchAngle = -45
			height = 90
			alt = 90
			asl = 140
		} else if waylineManage.WaylineFileID == 294 {
			// 特别保护区巡查航线
			gimbalPitchAngle = -45
			height = 100
			alt = 100
			asl = 150
		} else if waylineManage.WaylineFileID == 297 {
			// 接触网巡查
			gimbalPitchAngle = 0
			height = 78.7
			alt = 78.7
			asl = 57.6
		} else if waylineManage.WaylineFileID == 298 {
			// 桥梁巡查
			gimbalPitchAngle = 0
			height = 53.5
			alt = 53.5
			asl = 53.5
		}
	} else if jobRecord.CompanyID == 93 {
		waylineManage, err := NewWaylineManageService().GetWaylineManageById(jobRecord.WaylineManageID)
		if err != nil {
			return "", fmt.Errorf("waylineManage: %d, err: %v", jobRecord.WaylineManageID, err)
		}

		if waylineManage.ID == 134 {
			// 路面抛洒物
			docxTemplateName = "report_template_road.docx"
			gimbalPitchAngle = -90
			height = 268.8
			asl = 268.8
		} else if waylineManage.ID == 135 {
			// 路面病害
			docxTemplateName = "report_template_road.docx"
			gimbalPitchAngle = -90
			height = 267.5
			asl = 267.5
		} else if waylineManage.ID == 136 {
			// 路面病害
			docxTemplateName = "report_template_road.docx"
			gimbalPitchAngle = 0
			height = 260
			asl = 260
		} else if waylineManage.ID == 139 {
			// 桥梁
			docxTemplateName = "report_template_bridge.docx"
			area = 16.69
			areaTopAsl = 247.8
			areaBottomAsl = 228.4
			gimbalPitchAngle = 0
		} else if waylineManage.ID == 146 {
			docxTemplateName = "report_template_road.docx"
			gimbalPitchAngle = -45
			height = 260
			asl = 260
		} else {
			docxTemplateName = "report_template_road.docx"
			gimbalPitchAngle = -45
			height = 260
			asl = 260
		}
	} else if jobRecord.CompanyID == 94 {
		waylineManage, err := NewWaylineManageService().GetWaylineManageById(jobRecord.WaylineManageID)
		if err != nil {
			return "", fmt.Errorf("waylineManage: %d, err: %v", jobRecord.WaylineManageID, err)
		}

		if waylineManage.ID == 179 || waylineManage.ID == 211 {
			// 地保
			docxTemplateName = "report_template_fuzhou.docx"
			gimbalPitchAngle = -45
			height = 50
			alt = 45
			asl = 50
		} else if waylineManage.ID == 167 {
			// 桥梁
			docxTemplateName = "report_template_fuzhou.docx"
			gimbalPitchAngle = 0
			height = 20
			alt = 10
			asl = 20
		} else if waylineManage.ID == 191 {
			// cad叠加
			docxTemplateName = "report_template_fuzhou.docx"
			gimbalPitchAngle = -90
			height = 50
			alt = 50
			asl = 60
		} else if waylineManage.ID == 209 {
			// 接触网巡查
			docxTemplateName = "report_template_fuzhou.docx"
			gimbalPitchAngle = 0
			height = 50
			alt = 45
			asl = 50
		} else if waylineManage.ID == 214 {
			// 桥梁绕飞
			docxTemplateName = "report_template_xm_214.docx"
			gimbalPitchAngle = 0
			height = 20
			alt = 10
			asl = 20
		} else {
			docxTemplateName = "report_template_fuzhou.docx"
		}
	} else {
		docxTemplateName = "report_template_none.docx"
	}

	flightParams := map[string]interface{}{
		"${wayline_name}":            jobRecord.WaylineName,
		"${point_total}":             len(points),
		"${mileage}":                 utils.TruncateFloat(jobRecord.FlightMileage, 2),
		"${time}":                    utils.TruncateFloat(jobRecord.FlightDuration, 2),
		"${height}":                  height,
		"${mode}":                    mode,
		"${lost}":                    lostAction,
		"${finish}":                  finishAction,
		"${speed}":                   kmlFlightParams.AutoFlightSpeed,
		"${horizontal_rate}":         kmlFlightParams.AutoFlightSpeed,
		"${alt}":                     alt,
		"${warn_total}":              warnTotal,
		"${warn_desc}":               warnDesc,
		"${asl}":                     asl,
		"${gear}":                    "N",
		"${gimbal_pitch_angle}":      gimbalPitchAngle,
		"${rtk_total}":               random,
		"${obstacle_avoidance_info}": "正常",
		"area":                       area,
		"top":                        areaTopAsl,
		"bottom":                     areaBottomAsl,
	}

	if jobRecord.TaskTrajectoryPic != "" {
		waylinePic, err := ossSvc.GetObjectSignURL(jobRecord.TaskTrajectoryPic, 60*60)
		if err != nil {
			return "", err
		}

		flightParams["${TaskTrajectoryPic}"] = waylinePic
	}

	flightParamsBytes, err := json.Marshal(flightParams)
	if err != nil {
		return "", err
	}

	warnRecordListBytes, err := json.Marshal(warnRecordList)
	if err != nil {
		return "", err
	}

	conn, err := grpc.Dial(config.GetConfig().Helper.Address, grpc.WithInsecure(), grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(1024*1024*12)))
	if err != nil {
		return "", err
	}
	defer conn.Close()

	params := &pb.GenReportRequest{
		FlightParams:     string(flightParamsBytes),
		WarnRecord:       string(warnRecordListBytes),
		JobId:            jobRecord.WaylineJobID,
		DocxTemplateName: docxTemplateName,
	}

	resp, err := pb.NewHelperServiceClient(conn).GenReport(context.Background(), params)
	if err != nil {
		return "", err
	}

	if !resp.Succeed {
		err = fmt.Errorf(resp.Msg)
		repo.GetLogger().Errorf("failed to gen report, err: %v", err)
		//return "", err
	}

	return resp.DocxObjectKey, nil
}

func findClosestPosition(arr []dto.Trajectory, target float64) dto.Trajectory {
	if len(arr) == 0 {
		panic("数组不能为空")
	}

	closest := arr[0]
	minDiff := math.Abs(target - float64(closest.Timestamp))

	for _, i := range arr[1:] {
		diff := math.Abs(target - float64(i.Timestamp))
		if diff < minDiff {
			minDiff = diff
			closest = i
		}
	}

	return closest
}

func filterAlarms(alarms []dto.AlarmDto) []dto.AlarmDto {
	if len(alarms) == 0 {
		return alarms
	}

	// 结果数组，包含第一个元素
	filtered := []dto.AlarmDto{alarms[0]}

	for i := 1; i < len(alarms); i++ {
		// 如果当前元素与前一个已加入的元素时间差大于等于 5 秒，加入结果数组
		if alarms[i].TimeSec-filtered[len(filtered)-1].TimeSec >= 5 {
			filtered = append(filtered, alarms[i])
		}
	}

	return filtered
}

// sendWarningSMS 发送警告短信
func (s *JobDataProcessorService) sendWarningSMS(jobRecord *model.InspectionJobRecord, jobRecordID int, companyID int) {
	if jobRecord.PersonNumber == "" {
		return
	}

	conf := config.GetConfig()
	// 创建短信客户端
	config := &client.SMSConfig{
		ApId:      conf.Sms.ApId,
		SecretKey: conf.Sms.SecretKey,
		EcName:    conf.Sms.EcName,
		Sign:      conf.Sms.Sign,
		AddSerial: conf.Sms.AddSerial,
		URL:       conf.Sms.Url,
	}
	smsClient := client.NewSMSClient(config)

	formattedTime := time.Now().Format("15:04:05")

	warnRecords := make([]model.JobWarnRecord, 0)

	op := s.db.Model(&model.JobWarnRecord{}).Where("company_id = ?", jobRecord.CompanyID)
	op.Where("job_record_id = ?", jobRecordID)

	err := op.Scan(&warnRecords).Error
	if err != nil {
		s.logger.Errorf("sendWarningSMS: scan warnRecords failed, companyID: %d, jobRecordID: %d, err: %v", jobRecord.CompanyID, jobRecordID, err)
		return
	}

	if len(warnRecords) > 0 {
		var warnDesc []string
		warnDescMap := make(map[string]bool)
		for _, record := range warnRecords {
			if !warnDescMap[record.WarnDesc] {
				warnDescMap[record.WarnDesc] = true
				warnDesc = append(warnDesc, record.WarnDesc)
			}
		}

		var link string
		var content string

		if companyID == 94 {
			link = fmt.Sprintf("http://xm.boeing.meitengtech.com/static/warn-notice.html?type=%s&id=%d", "inspection_job_record", jobRecordID)
			content = fmt.Sprintf("【厦门轨道智慧地保警告】\n厦门地铁四号线地保警报：系统于%s发现%s，请点击链接查看告警内容与告警位置\n%s", formattedTime, strings.Join(warnDesc, "、"), link)
		}
		if companyID == 91 {
			link = fmt.Sprintf("http://test.boeing.meitengtech.com/static/warn-notice.html?type=%s&id=%d", "inspection_job_record", jobRecordID)
			content = fmt.Sprintf("【福州轨道智慧地保警告】\n福州地铁地保警报：系统于%s发现%s，请点击链接查看告警内容与告警位置\n%s", formattedTime, strings.Join(warnDesc, "、"), link)
		}

		// 获取详细发送结果
		_, err = smsClient.GetSendResult(jobRecord.PersonNumber, content, false)
		if err != nil {
			s.logger.Errorf("sendWarningSMS: send SMS failed, phone: %s, companyID: %d, err: %v", jobRecord.PersonNumber, companyID, err)
			return
		}
	}
}

func (s *JobDataProcessorService) checkAlarmsAggregatableByPosition(warnRecord *model.JobWarnRecord, targetWarnRecord *model.JobWarnRecord, aggregationDistance float64) bool {
	// 获取当前记录的位置信息
	var centerPosition dto.JobWarnRecordPosition

	if e := json.Unmarshal([]byte(warnRecord.Position), &centerPosition); e != nil {
		s.logger.Errorf("failed to decode job warn record position, id: %d, err: %v", warnRecord.ID, e)
	}
	// 获取待比较记录的位置信息
	var comparePosition dto.JobWarnRecordPosition

	if e := json.Unmarshal([]byte(targetWarnRecord.Position), &comparePosition); e != nil {
		s.logger.Errorf("failed to decode job warn record position, id: %d, err: %v", warnRecord.ID, e)
	}

	// 计算两点之间的距离
	dis := utils.GetDistanceInMeter(
		centerPosition.Standard.Lat,
		centerPosition.Standard.Lng,
		comparePosition.Standard.Lat,
		comparePosition.Standard.Lng,
	)

	// 如果距离小于等于指定距离，则加入当前聚合组
	if dis <= aggregationDistance {
		return true
	}

	return false
}

func (s *JobDataProcessorService) checkForDuplicateAlarms(warnRecord *model.JobWarnRecord, jobWarnRecords []model.JobWarnRecord) bool {
	// 遍历已有的告警记录
	for _, existingRecord := range jobWarnRecords {
		// 检查位置是否在50米内
		// 获取当前记录的位置信息
		var centerPosition dto.JobWarnRecordPosition

		if e := json.Unmarshal([]byte(warnRecord.Position), &centerPosition); e != nil {
			s.logger.Errorf("failed to decode job warn record position, id: %d, err: %v", warnRecord.ID, e)
		}
		// 获取待比较记录的位置信息
		var comparePosition dto.JobWarnRecordPosition

		if e := json.Unmarshal([]byte(existingRecord.Position), &comparePosition); e != nil {
			s.logger.Errorf("failed to decode job warn record position, id: %d, err: %v", warnRecord.ID, e)
		}

		// 计算两点之间的距离
		dis := utils.GetDistanceInMeter(
			centerPosition.Standard.Lat,
			centerPosition.Standard.Lng,
			comparePosition.Standard.Lat,
			comparePosition.Standard.Lng,
		)

		// 如果距离小于等于50.0，且告警类型相同，则为重复告警记录
		if dis <= 50.0 && existingRecord.WarnType == warnRecord.WarnType && existingRecord.AlgorithmSource == warnRecord.AlgorithmSource {
			return true
		}
	}
	return false // 非重复告警记录
}

// CompactAlarms  告警聚合，将传入的告警记录聚合到告警中
func (s *JobDataProcessorService) CompactAlarms(warnRecord *model.JobWarnRecord, jobRecordID int, aggregationDistance float64) error {
	// 根据job_record_id查询job_warn，获取job_warn数组
	var jobWarns []model.JobWarn
	op := s.db.Model(&model.JobWarn{}).Where("job_record_id = ?", jobRecordID)
	err := op.Scan(&jobWarns).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		s.logger.Errorf("CompactAlarms: scan jobWarns failed, jobRecordID: %d, err: %v", jobRecordID, err)
		return err
	}

	// 遍历job_warn数组，根据job_warn的id查询第一条job_warn_record的记录
	for _, jobWarn := range jobWarns {
		if jobWarn.Level != warnRecord.Level {
			continue
		}

		var jobWarnRecords []model.JobWarnRecord
		op := s.db.Model(&model.JobWarnRecord{}).Where("job_warn_id = ?", jobWarn.ID)
		err := op.Scan(&jobWarnRecords).Error
		if err != nil && err != gorm.ErrRecordNotFound {
			s.logger.Errorf("CompactAlarms: scan jobWarnRecords failed, jobWarnID: %d, err: %v", jobWarn.ID, err)
			continue
		}

		// 将传入的job_warn_record和第一条job_warn_record的记录做聚合，判断是否能聚合
		if s.checkAlarmsAggregatableByPosition(warnRecord, &jobWarnRecords[0], aggregationDistance) {
			// 如果能聚合，再做重复告警判断
			if !s.checkForDuplicateAlarms(warnRecord, jobWarnRecords) {
				// 如果不重复，将该告警记录的job_warn_id修改为job_warn的id
				warnRecord.JobWarnID = jobWarn.ID
				// 保存到数据库
				err := s.db.Save(warnRecord).Error
				if err != nil {
					s.logger.Errorf("CompactAlarms: save warnRecord failed, warnRecordID: %d, err: %v", warnRecord.ID, err)
					return err
				}
				return nil
			}
		}
	}
	// 如果没有符合当前告警记录的告警等级且不能被聚合，则创建新的告警
	jobRecord, err := NewInspectionJobRecordService().GetById(jobRecordID)
	if err != nil {
		s.logger.Errorf("HandleNoRealTimePicDataResp: failed to get job record, err: %v", err)
		return err
	}
	// fixme 先写死
	var warnType []string
	if jobRecord.Scene == enum.JobSceneTypeEnum_Bridge.Value() {
		warnType = []string{"桥梁病害"}
	} else if jobRecord.Scene == enum.JobSceneTypeEnum_Road.Value() {
		warnType = []string{"路面病害"}
	} else if jobRecord.Scene == enum.JobSceneTypeEnum_SubwayGround.Value() {
		warnType = []string{"违规施工", "异物侵占"}
	}

	warnTypeJson, err := json.Marshal(warnType)
	if err != nil {
		s.logger.Errorf("HandleNoRealTimePicDataResp: failed to marshal warn type, err: %v", err)
		return err
	}

	jobWarn := model.JobWarn{
		CompanyID:     jobRecord.CompanyID,
		JobRecordID:   jobRecord.ID,
		JobRecordName: jobRecord.Name,
		WarnType:      string(warnTypeJson),
		Level:         warnRecord.Level,
		Status:        2,
		WarnAt:        warnRecord.WarnAt,
	}
	tx := s.db.Begin()

	if err = tx.Create(&jobWarn).Error; err != nil {
		s.logger.Errorf("HandleNoRealTimeDataResp: failed to create warn video, err: %v", err)
		tx.Rollback()
		return err
	}
	warnRecord.JobWarnID = jobWarn.ID
	if err := tx.Save(warnRecord).Error; err != nil {
		s.logger.Errorf("CompactAlarms: save warnRecord failed, warnRecordID: %d, err: %v", warnRecord.ID, err)
		tx.Rollback()
		return err
	}
	tx.Commit()
	return nil
}
