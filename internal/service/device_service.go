package service

import (
	"encoding/json"
	"strconv"
	"strings"
	"sync"
	"time"
	control_cloud_enum "wukong-api/cloud_sdk/cloud_api/control/cloud_enum"
	"wukong-api/cloud_sdk/cloud_api/device/cloud_enum"
	"wukong-api/cloud_sdk/cloud_api/device/request"
	device_request "wukong-api/cloud_sdk/cloud_api/device/request"
	"wukong-api/cloud_sdk/mqtt"
	"wukong-api/internal/cloudinfra"
	"wukong-api/internal/dto"
	"wukong-api/internal/model"
	"wukong-api/internal/repo"

	"github.com/qxsugar/pkg/dbx"

	"github.com/thoas/go-funk"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	deviceServiceOnce sync.Once
	deviceService     *DeviceService
)

type DeviceService struct {
	db                     *gorm.DB
	logger                 *zap.SugaredLogger
	deviceRedisService     *DeviceRedisService
	liveStreamService      *LiveStreamService
	capacityCameraService  *CapacityCameraService
	liveStreamRedisService *LiveStreamRedisService
}

func NewDeviceService() *DeviceService {
	deviceServiceOnce.Do(func() {
		deviceService = &DeviceService{
			db:                     repo.GetDatabase(),
			logger:                 repo.GetLogger(),
			deviceRedisService:     NewDeviceRedisService(),
			liveStreamService:      NewLiveStreamService(),
			capacityCameraService:  NewCapacityCameraService(),
			liveStreamRedisService: NewLiveStreamRedisService(),
		}
	})
	return deviceService
}

func (s *DeviceService) List(offset int, limit int, domain *int, cid int) ([]model.Device, int64, error) {
	list := make([]model.Device, 0, limit)
	var total int64

	op := s.db.Model(&model.Device{}).
		Joins("JOIN company_device ON device.device_sn = company_device.device_sn").
		Where("company_device.company_id = ?", cid).
		Order("id desc")

	if domain != nil {
		op = op.Where("device.domain = ?", domain)
	}

	if err := op.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if err := op.Offset(offset).Limit(limit).Find(&list).Error; err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

// getDockCountByCid 机场（对应的无人机不为空）数量
func (s *DeviceService) getDockCountByCid(cid int) (int64, error) {
	var total int64

	op := s.db.Model(&model.Device{}).
		Joins("JOIN company_device ON device.device_sn = company_device.device_sn").
		Where("company_device.company_id = ?", cid).
		Where("device.domain = ? AND device.child_sn IS NOT NULL AND device.child_sn != ''", cloud_enum.Dock.Value())

	err := op.Count(&total).Error

	if err != nil {
		return 0, err
	}

	return total, nil
}

func (s *DeviceService) firmwareVersionToInt(version string) (int, error) {
	versionWithoutDots := strings.ReplaceAll(version, ".", "")
	return strconv.Atoi(versionWithoutDots)
}

func (s *DeviceService) GetDeviceBySn(sn string) (*model.Device, error) {
	var d *model.Device
	err := s.db.Model(&model.Device{}).Where("device_sn = ?", sn).First(&d).Error
	if err != nil {
		return nil, err
	}
	return d, nil
}

func (s *DeviceService) GetDeviceWithCompanyBySn(sn string) (*model.DeviceWithCompany, error) {
	var result dto.DeviceWithCompanyDTO

	err := s.db.Model(&model.Device{}).
		Select("device.*, GROUP_CONCAT(DISTINCT company_device.company_id) as company_ids").
		Joins("LEFT JOIN company_device ON device.device_sn = company_device.device_sn").
		Where("device.device_sn = ?", sn).
		Group("device.id").
		Scan(&result).Error
	if err != nil {
		return nil, err
	}

	deviceWithCompany := model.DeviceWithCompany{
		ID:                 result.ID,
		DeviceSN:           result.DeviceSN,
		DeviceName:         result.DeviceName,
		Nickname:           result.Nickname,
		ChildSN:            result.ChildSN,
		DeviceDesc:         result.DeviceDesc,
		FirmwareVersion:    result.FirmwareVersion,
		CreatedAt:          result.CreatedAt,
		UpdatedAt:          result.UpdatedAt,
		IsDeleted:          result.IsDeleted,
		DeviceType:         result.DeviceType,
		SubType:            result.SubType,
		Domain:             result.Domain,
		DeviceIndex:        result.DeviceIndex,
		UserID:             result.UserID,
		CompatibleStatus:   result.CompatibleStatus,
		BoundStatus:        result.BoundStatus,
		BoundTime:          result.BoundTime,
		LoginTime:          result.LoginTime,
		JobId:              result.JobId,
		LiveStreamServerID: result.LiveStreamServerID,
	}

	// 将 GROUP_CONCAT 的结果转换为整数数组
	if result.CompanyIds != "" {
		companyIdsStr := strings.Split(result.CompanyIds, ",")
		companyIds := make([]int, 0, len(companyIdsStr))
		for _, idStr := range companyIdsStr {
			var id int
			if id, err = strconv.Atoi(idStr); err == nil {
				companyIds = append(companyIds, id)
			}
		}
		deviceWithCompany.CompanyID = companyIds
	}

	return &deviceWithCompany, nil
}

// IsDeviceIsJob 判断机场是否有任务
func (s *DeviceService) IsDeviceIsJob(sn string) (bool, error) {
	d, err := s.GetDeviceBySn(sn)
	if err != nil {
		return false, nil
	}
	return d.JobId != "", nil
}

func (s *DeviceService) getDeviceList(cid int) ([]model.Device, error) {
	var list []model.Device
	err := s.db.Model(&model.Device{}).
		Joins("JOIN company_device ON device.device_sn = company_device.device_sn").
		Where("company_device.company_id = ?", cid).
		Find(&list).Error
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s *DeviceService) getDeviceByList(deviceSn string, deviceList []model.Device) *model.Device {
	for _, device := range deviceList {
		if device.DeviceSN == deviceSn {
			return &device
		}
	}
	return nil
}

func (s *DeviceService) getAllDock() ([]model.Device, error) {
	var dockList []model.Device
	err := s.db.Model(model.Device{}).Where("domain = ?", cloud_enum.Dock.Value()).Find(&dockList).Error
	if err != nil {
		return nil, err
	}

	return dockList, nil
}

func (s *DeviceService) getAllDockByCid(cid int) ([]model.Device, error) {
	var list []model.Device
	err := s.db.Model(&model.Device{}).
		Joins("JOIN company_device ON device.device_sn = company_device.device_sn").
		Where("company_device.company_id = ?", cid).
		Where("device.domain = ?", cloud_enum.Dock.Value()).
		Find(&list).Error

	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s *DeviceService) getAllDockAndDrone() ([]model.Device, error) {
	var deviceList []model.Device
	err := s.db.Model(model.Device{}).Where("domain = ?", cloud_enum.Dock.Value()).Or("domain = ?", cloud_enum.SubDevice.Value()).Find(&deviceList).Error
	if err != nil {
		return nil, err
	}

	return deviceList, nil
}

func (s *DeviceService) getAllOnlineDevice() ([]model.Device, error) {
	var deviceList []model.Device

	err := s.db.Model(&model.Device{}).Where("status = ?", 1).Find(&deviceList).Error
	if err != nil {
		return nil, err
	}

	return deviceList, nil
}

func (s *DeviceService) getDeviceByChildSn(sn string) (*model.Device, error) {
	var d *model.Device
	err := s.db.Model(&model.Device{}).Where("child_sn = ?", sn).First(&d).Error
	if err != nil {
		return nil, err
	}
	return d, nil
}

func (s *DeviceService) getDeviceCompanyByChildSn(sn string) (*model.DeviceWithCompany, error) {
	var result dto.DeviceWithCompanyDTO

	err := s.db.Model(&model.Device{}).
		Select("device.*, GROUP_CONCAT(DISTINCT company_device.company_id) as company_ids").
		Joins("LEFT JOIN company_device ON device.child_sn = company_device.device_sn").
		Where("device.child_sn = ?", sn).
		Group("device.id").
		Scan(&result).Error
	if err != nil {
		return nil, err
	}

	deviceWithCompany := model.DeviceWithCompany{
		ID:                 result.ID,
		DeviceSN:           result.DeviceSN,
		DeviceName:         result.DeviceName,
		Nickname:           result.Nickname,
		ChildSN:            result.ChildSN,
		DeviceDesc:         result.DeviceDesc,
		FirmwareVersion:    result.FirmwareVersion,
		CreatedAt:          result.CreatedAt,
		UpdatedAt:          result.UpdatedAt,
		IsDeleted:          result.IsDeleted,
		DeviceType:         result.DeviceType,
		SubType:            result.SubType,
		Domain:             result.Domain,
		DeviceIndex:        result.DeviceIndex,
		UserID:             result.UserID,
		CompatibleStatus:   result.CompatibleStatus,
		BoundStatus:        result.BoundStatus,
		BoundTime:          result.BoundTime,
		LoginTime:          result.LoginTime,
		JobId:              result.JobId,
		LiveStreamServerID: result.LiveStreamServerID,
	}

	// 将 GROUP_CONCAT 的结果转换为整数数组
	if result.CompanyIds != "" {
		companyIdsStr := strings.Split(result.CompanyIds, ",")
		companyIds := make([]int, 0, len(companyIdsStr))
		for _, idStr := range companyIdsStr {
			var id int
			if id, err = strconv.Atoi(idStr); err == nil {
				companyIds = append(companyIds, id)
			}
		}
		deviceWithCompany.CompanyID = companyIds
	}

	return &deviceWithCompany, nil
}

func (s *DeviceService) updateDeviceJob(sn string, jobId string) error {
	err := s.db.Model(&model.Device{}).Where("device_sn = ?", sn).Update("job_id", jobId).Error
	if err != nil {
		return err
	}

	deviceData, err := s.GetDeviceBySn(sn)
	if err != nil {
		return err
	}

	err = s.deviceRedisService.setDeviceOnlineWithModel(*deviceData)
	if err != nil {
		return err
	}

	return nil
}

func (s *DeviceService) OsdDockHandle(req mqtt.TopicOsdRequest[device_request.OsdDock], deviceData *model.Device, payload []byte) (err error) {
	//if req.Data.JobNumber != nil {
	//	s.logger.Info("jobNumber: %s", string(payload))
	//}
	//if req.Data.DrcState != nil {
	//	s.logger.Info("drcState: %s", string(payload))
	//}

	oldDockIsOnline, err := s.deviceRedisService.CheckDeviceOnline(deviceData.DeviceSN)
	if err != nil {
		return err
	}

	if !oldDockIsOnline {
		err = s.updateDeviceToOnline(deviceData.DeviceSN)
		if err != nil {
			return err
		}
	}

	err = s.deviceRedisService.setDeviceOnlineWithModel(*deviceData)
	if err != nil {
		return err
	}

	dockSn := req.Gateway

	if err = s.fillDockOsd(dockSn, &req.Data, oldDockIsOnline); err != nil {
		return err
	}

	return nil
}

func (s *DeviceService) StateDockHandle(req mqtt.TopicStateRequest[device_request.StateDock], payload string) error {
	if req.Data.LiveCapacity != nil {
		//s.logger.Info("dockLiveCapacity: %s", payload)
		if err := s.liveStreamService.dockLivestreamAbilityUpdate(req.Data.LiveCapacity, req.Gateway, req.Timestamp); err != nil {
			s.logger.Errorf("StateDockHandle, dockLivestreamAbilityUpdate gatewaySn: %s, err: %v", req.Gateway, err)
			return err
		}

		deviceSnList := funk.Map(req.Data.LiveCapacity.DeviceList, func(device device_request.LiveCapacityDevice) string { return device.Sn }).([]string)
		//s.logger.Info("dockLiveCapacity: deviceSnList: %v", deviceSnList)

		if err := s.liveStreamService.StartLiveStreamWithDockCapacityUp(deviceSnList); err != nil {
			s.logger.Errorf("StateDockHandle, StartLiveStreamWithDockCapacityUp gatewaySn: %s, err: %v", req.Gateway, err)
			return err
		}
	}

	if req.Data.LiveStatus != nil {
		if err := s.liveStreamService.dockLivestreamStatusUpdate(*req.Data.LiveStatus); err != nil {
			return err
		}
	}

	if req.NeedReply == 1 {
		err := cloudinfra.StateReply(req.Gateway, req.Bid, req.Tid)
		if err != nil {
			s.logger.Errorf("StateDockHandle, StateReply gatewaySn: %s, err: %v", req.Gateway, err)
			return err
		}
	}

	return nil
}

func (s *DeviceService) StateRcHandle(req mqtt.TopicStateRequest[device_request.StateRC]) error {
	if req.Data.LiveCapacity != nil {
		if err := s.liveStreamService.rcLivestreamAbilityUpdate(req.Data.LiveCapacity, req.Gateway, req.Timestamp); err != nil {
			return err
		}

		deviceSnList := funk.Map(req.Data.LiveCapacity.DeviceList, func(device device_request.LiveCapacityDevice) string { return device.Sn }).([]string)
		if err := s.liveStreamService.StartLiveStreamWithRcCapacityUp(deviceSnList); err != nil {
			return err
		}
	}

	if req.Data.LiveStatus != nil {
		if err := s.liveStreamService.rcLivestreamStatusUpdate(*req.Data.LiveStatus, req.Gateway); err != nil {
			return err
		}
	}

	if req.Data.DongleInfo != nil {
	}

	if req.Data.FirmwareVersion != nil {
	}

	if req.NeedReply == 1 {
		err := cloudinfra.StateReply(req.Gateway, req.Bid, req.Tid)
		if err != nil {
			s.logger.Errorf("StateRcHandle, StateReply gatewaySn: %s, err: %v", req.Gateway, err)
			return err
		}
	}

	return nil
}

func (s *DeviceService) StateDroneHandle(req mqtt.TopicStateRequest[device_request.StateDrone]) error {
	gateway, err := s.deviceRedisService.getDeviceOnline(req.Gateway)
	if err != nil {
		return err
	}

	if gateway == nil {
		return nil
	}

	if gateway.Domain == cloud_enum.Dock.Value() {
		if req.Data.ControlSource != nil {
			if err = s.dockControlSourceUpdate(req.Gateway, req.Data); err != nil {
				return err
			}
		}
	}

	if req.Data.ModeCodeReason != nil && *req.Data.ModeCodeReason != 0 {
		s.logger.Debug("drone: %s, mode_code_reason: %d", req.Gateway, *req.Data.ModeCodeReason)
	}

	if req.NeedReply == 1 {
		err = cloudinfra.StateReply(req.Gateway, req.Bid, req.Tid)
		if err != nil {
			s.logger.Errorf("StateDroneHandle, StateReply gatewaySn: %s, err: %v", req.Gateway, err)
			return err
		}
	}

	return nil
}

// dockControlSourceUpdate 更新网关设备控制权
// 消息示例
// {
// "control_source": "A",
// "home_latitude": 39.037475661961928,
// "home_longitude": 117.15269210498018,
// "locked": false,
// "low_battery_warning_threshold": 20,
// "payloads": [
// {
// "control_source": "A",
// "payload_index": "52-0-0",
// "sn": "1581F5BLD232B00138U1-0"
// }
// ],
// "serious_low_battery_warning_threshold": 10
// }
func (s *DeviceService) dockControlSourceUpdate(gatewaySn string, data device_request.StateDrone) error {
	if cloud_enum.ControlSource_UNKNOWN == *data.ControlSource {
		return nil
	}

	dock, _ := s.deviceRedisService.getDeviceOnline(gatewaySn)
	if dock == nil {
		return nil
	}

	droneSn := dock.ChildSN
	drone, _ := s.deviceRedisService.getDeviceOnline(droneSn)
	if drone == nil {
		return nil
	}

	// 更新飞行控制权
	if err := s.updateFlightControl(dock, *data.ControlSource); err != nil {
		return err
	}

	// 更新负载权
	err := NewDevicePayloadService().updatePayloadControl(drone, data.Payloads)
	if err != nil {
		return err
	}

	return nil
}

func (s *DeviceService) updateFlightControl(gateway *dto.DeviceDTO, controlSource cloud_enum.ControlSourceEnum) error {
	if controlSource.Value() == gateway.DeviceIndex {
		return nil
	}

	gateway.DeviceIndex = controlSource.Value()
	if err := s.deviceRedisService.setDeviceOnline(*gateway); err != nil {
		return err
	}

	s.logger.Debugf("flight control source update, gateway sn: %s, control source %s", gateway.DeviceSN, controlSource.Value())

	return nil
}

func (s *DeviceService) fillDockOsd(dockSn string, dock *device_request.OsdDock, oldDockIsOnline bool) (err error) {
	var oldDock *request.OsdDock
	if oldDockIsOnline {
		oldDock, err = s.deviceRedisService.getDockOsd(dockSn)
		if err != nil {
			return err
		}
	}

	if oldDock == nil {
		if err = s.deviceRedisService.setDockOsd(dockSn, *dock); err != nil {
			return err
		}
		return nil
	}

	// 合并新旧数据
	mergedDock := *oldDock

	// 识别上报批次并合并数据
	// 第一批数据通常包含ModeCode
	if dock.ModeCode != nil {
		// 更新第一批数据字段
		mergedDock.NetworkState = dock.NetworkState
		mergedDock.DroneInDock = dock.DroneInDock
		mergedDock.DroneChargeState = dock.DroneChargeState
		mergedDock.Rainfall = dock.Rainfall
		mergedDock.WindSpeed = dock.WindSpeed
		mergedDock.EnvironmentTemperature = dock.EnvironmentTemperature
		mergedDock.Temperature = dock.Temperature
		mergedDock.Humidity = dock.Humidity
		mergedDock.Latitude = dock.Latitude
		mergedDock.Longitude = dock.Longitude
		mergedDock.Height = dock.Height
		mergedDock.AlternateLandPoint = dock.AlternateLandPoint
		mergedDock.FirstPowerOn = dock.FirstPowerOn
		mergedDock.PositionState = dock.PositionState
		mergedDock.Storage = dock.Storage
		mergedDock.ModeCode = dock.ModeCode
		mergedDock.CoverState = dock.CoverState
		mergedDock.SupplementLightState = dock.SupplementLightState
		mergedDock.EmergencyStopState = dock.EmergencyStopState
		mergedDock.AirConditioner = dock.AirConditioner
		mergedDock.BatteryStoreMode = dock.BatteryStoreMode
		mergedDock.AlarmState = dock.AlarmState
		mergedDock.PutterState = dock.PutterState
		mergedDock.SubDevice = dock.SubDevice
	}

	// 第二批数据通常包含JobNumber或维护相关信息
	if dock.JobNumber != nil || dock.MaintainStatus.MaintainStatusArray != nil {
		// 更新第二批数据字段
		if dock.JobNumber != nil {
			mergedDock.JobNumber = dock.JobNumber
		}
		if dock.AccTime != nil {
			mergedDock.AccTime = dock.AccTime
		}
		mergedDock.ActivationTime = dock.ActivationTime
		if len(dock.MaintainStatus.MaintainStatusArray) > 0 {
			mergedDock.MaintainStatus = dock.MaintainStatus
		}
		mergedDock.ElectricSupplyVoltage = dock.ElectricSupplyVoltage
		if dock.WorkingVoltage != nil {
			mergedDock.WorkingVoltage = dock.WorkingVoltage
		}
		mergedDock.WorkingCurrent = dock.WorkingCurrent
		mergedDock.BackupBattery = dock.BackupBattery
		mergedDock.DroneBatteryMaintenanceInfo = dock.DroneBatteryMaintenanceInfo
	}

	// 第三批数据通常包含DrcState或无线连接相关信息
	if dock.DrcState != nil || dock.WirelessLink != nil || dock.SDR != nil || dock.FlighttaskStepCode != nil {
		// 更新第三批数据字段
		if dock.DrcState != nil {
			mergedDock.DrcState = dock.DrcState
		}
		mergedDock.FlighttaskPrepareCapacity = dock.FlighttaskPrepareCapacity
		if dock.FlighttaskStepCode != nil {
			mergedDock.FlighttaskStepCode = dock.FlighttaskStepCode
		}
		if dock.MediaFileDetail.RemainUpload > 0 {
			mergedDock.MediaFileDetail = dock.MediaFileDetail
		}
		if dock.SDR != nil {
			mergedDock.SDR = dock.SDR
		}
		if dock.UserExperienceImprovement != nil {
			mergedDock.UserExperienceImprovement = dock.UserExperienceImprovement
		}
		if dock.WirelessLink != nil {
			mergedDock.WirelessLink = dock.WirelessLink
		}
	}

	// 保存合并后的数据
	if err = s.deviceRedisService.setDockOsd(dockSn, mergedDock); err != nil {
		return err
	}

	return nil
}

func (s *DeviceService) OsdDroneHandle(req mqtt.TopicOsdRequest[json.RawMessage], deviceData *model.Device) error {
	gateway, err := s.deviceRedisService.getDeviceOnline(req.Gateway)
	if err != nil {
		return err
	}

	if gateway == nil {
		return nil
	}

	oldDroneIsOnline, err := s.deviceRedisService.CheckDeviceOnline(deviceData.DeviceSN)
	if err != nil {
		return err
	}

	if !oldDroneIsOnline {
		err = s.updateDeviceToOnline(deviceData.DeviceSN)
		if err != nil {
			return err
		}
	}

	err = s.deviceRedisService.setDeviceOnlineWithModel(*deviceData)
	if err != nil {
		return err
	}

	err = s.deviceRedisService.setDroneOsd(deviceData.DeviceSN, req.Data)
	if err != nil {
		return err
	}

	return nil
}

func (s *DeviceService) getDockMode(dockSn string) *cloud_enum.DockModeCodeEnum {
	isOnline, _ := s.deviceRedisService.CheckDeviceOnline(dockSn)
	if !isOnline {
		return nil
	}

	dock, err := s.deviceRedisService.getDockOsd(dockSn)
	if err != nil || dock == nil {
		return nil
	}

	return dock.ModeCode
}

func (s *DeviceService) checkDockDrcMode(dockSn string) bool {
	isOnline, _ := s.deviceRedisService.CheckDeviceOnline(dockSn)
	if !isOnline {
		return false
	}

	dock, err := s.deviceRedisService.getDockOsd(dockSn)
	if err != nil || dock == nil {
		return false
	}

	if dock.DrcState == nil {
		return false
	}

	return dock.DrcState.Value() != control_cloud_enum.DISCONNECTED.Value()
}

// checkDockDrcModeWithDrcCmd DrcCmd调试用
func (s *DeviceService) checkDockDrcModeWithDrcCmd(dockSn string) bool {
	isOnline, _ := s.deviceRedisService.CheckDeviceOnline(dockSn)
	if !isOnline {
		s.logger.Warnf("checkDockDrcModeWithDrcCmd: is not in drc mode, dock is not online , dockSn: %s", dockSn)
		return false
	}

	dock, err := s.deviceRedisService.getDockOsd(dockSn)
	if err != nil || dock == nil {
		s.logger.Warnf("checkDockDrcModeWithDrcCmd: is not in drc mode, dock osd is nil , dockSn: %s", dockSn)
		return false
	}

	if dock.DrcState == nil {
		s.logger.Warnf("checkDockDrcModeWithDrcCmd: is not in drc mode, dock drc state is nil , dockSn: %s", dockSn)
		return false
	}

	if dock.DrcState.Value() == control_cloud_enum.DISCONNECTED.Value() {
		s.logger.Warnf("checkDockDrcModeWithDrcCmd: is not in drc mode, dock drc state is disconnected , dockSn: %s", dockSn)
	}

	return dock.DrcState.Value() != control_cloud_enum.DISCONNECTED.Value()
}

// checkAuthorityFlight 检查网关设备是否有飞行控制权
func (s *DeviceService) checkAuthorityFlight(gatewaySn string) bool {
	deviceOnline, _ := s.deviceRedisService.getDeviceOnline(gatewaySn)

	if deviceOnline != nil {
		return (cloud_enum.Dock.Value() == deviceOnline.Domain || cloud_enum.Gateway.Value() == deviceOnline.Domain) && cloud_enum.ControlSource_A.Value() == deviceOnline.DeviceIndex
	}

	return false
}

// checkDroneBatteryIsEnoughByDock 无人机电量检查
func (s *DeviceService) checkDroneBatteryIsEnoughByDock(dockSn string, minBatteryCapacity float64) (bool, int, error) {
	dockIsOnline, err := s.deviceRedisService.CheckDeviceOnline(dockSn)
	if !dockIsOnline {
		return false, 0, err
	}

	dev, err := s.GetDeviceBySn(dockSn)
	if err != nil {
		return false, 0, err
	}
	droneSn := dev.ChildSN

	var droneCapacityPercent int

	var droneOsd *request.OsdDockDrone
	droneIsOnline, err := s.deviceRedisService.CheckDeviceOnline(droneSn)
	if err != nil {
		return false, 0, err
	}

	if droneIsOnline {
		droneOsd, _ = s.deviceRedisService.getDockDroneOsd(droneSn)
	}

	if droneOsd != nil {
		droneCapacityPercent = droneOsd.Battery.CapacityPercent
	} else {
		if dockOsd, _ := s.deviceRedisService.getDockOsd(dockSn); dockOsd != nil {
			droneCapacityPercent = dockOsd.DroneChargeState.CapacityPercent
		}
	}

	if droneCapacityPercent >= int(minBatteryCapacity) {
		return true, droneCapacityPercent, err
	}

	return false, droneCapacityPercent, nil
}

func (s *DeviceService) getDockDroneMode(deviceSn string) cloud_enum.DroneModeCodeEnum {
	droneIsOnline, _ := s.deviceRedisService.CheckDeviceOnline(deviceSn)
	if !droneIsOnline {
		return cloud_enum.DroneModeCode_DISCONNECTED
	}

	drone, _ := s.deviceRedisService.getDockDroneOsd(deviceSn)
	if drone == nil {
		return cloud_enum.DroneModeCode_DISCONNECTED
	}

	return *drone.ModeCode
}

func (s *DeviceService) OsdRCHandle(req mqtt.TopicOsdRequest[device_request.OsdRC], deviceData *model.Device) (err error) {
	oldDeviceIsOnline, err := s.deviceRedisService.CheckDeviceOnline(deviceData.DeviceSN)
	if err != nil {
		return err
	}

	if !oldDeviceIsOnline {
		err = s.updateDeviceToOnline(deviceData.DeviceSN)
		if err != nil {
			return err
		}
	}

	err = s.deviceRedisService.setDeviceOnlineWithModel(*deviceData)
	if err != nil {
		return err
	}

	if err = s.deviceRedisService.setRcOsd(req.Gateway, req.Data); err != nil {
		return err
	}

	return nil
}

func (s *DeviceService) updateDeviceToOnline(deviceSn string) error {
	err := s.db.Model(model.Device{}).Where("device_sn = ?", deviceSn).Updates(&model.Device{
		Status: 1,
	}).Error

	if err != nil {
		return err
	}
	return nil
}

func (s *DeviceService) updateDeviceToOffline(deviceSn string) error {
	err := s.db.Debug().Model(model.Device{}).Where("device_sn = ?", deviceSn).Updates(&model.Device{
		OfflineTime: dbx.TimeStamp{Time: time.Now()},
		Status:      2,
	}).Error

	if err != nil {
		return err
	}
	return nil
}

func (s *DeviceService) deviceOffline(sn string) {
	s.logger.Infof("deviceOffline: sn: %s", sn)

	err := s.updateDeviceToOffline(sn)
	if err != nil {
		s.logger.Errorf("updateDeviceToOffline error: %v", err)
		return
	}

	//s.deviceRedisService.delDeviceOnline(sn)
	s.deviceRedisService.DelDeviceLiveStreaming(sn)
	s.capacityCameraService.delCapacityCameraByDeviceSn(sn)
	s.liveStreamRedisService.delLiveCapacityTimestamp(sn)
}

// DeviceWorker 设备的后台工作协程
func DeviceWorker() {
	service := NewDeviceService()

	// 设备状态监听
	go func() {
		ticker := time.NewTicker(5 * time.Second)
		for {
			<-ticker.C
			service.checkDeviceStatus()
		}
	}()

	// 每隔1小时刷新机场直播
	go func() {
		for {
			now := time.Now()
			// 计算下一个早上5点
			next := time.Date(now.Year(), now.Month(), now.Day(), 5, 0, 0, 0, now.Location())
			if now.After(next) {
				next = next.Add(24 * time.Hour)
			}

			// 等待到达指定时间
			timer := time.NewTimer(next.Sub(now))
			<-timer.C

			service.refreshDockLiveStream()

			// 立即执行一次刷新
			//service.refreshDockLiveStream()
			//
			//// 等待1小时
			//timer := time.NewTimer(1 * time.Hour)
			//<-timer.C
		}
	}()

}

// checkDeviceStatus 检查设备状态，离线设备作下线处理
func (s *DeviceService) checkDeviceStatus() {
	if CheckIsOccupiedPermissions() {
		return
	}

	deviceRedisSvc := NewDeviceRedisService()
	droneSvc := NewDroneService()
	drcRedisSvc := NewDockDRCRedisService()
	payloadRedisSvc := NewPayloadRedisService()

	devices, err := s.getAllOnlineDevice()
	if err != nil {
		return
	}

	for _, device := range devices {
		deviceSn := device.DeviceSN

		isOnline, _ := deviceRedisSvc.CheckDeviceOnline(deviceSn)
		if !isOnline {
			// 无人机下线
			if device.Domain == cloud_enum.SubDevice.Value() {
				gateway, _ := s.getDeviceByChildSn(deviceSn)
				if gateway != nil {
					drcRedisSvc.delDrcDataInRedis(gateway.DeviceSN)
				}

				var drone *model.Drone
				drone, err = droneSvc.GetDroneByVehicleID(deviceSn)
				if err != nil {
					continue
				}

				if drone.WorkStatus == Drone_Work_Status_Loss || drone.WorkStatus == Drone_Work_Status_Offline || drone.WorkStatus == Drone_Work_Status_Ready {
					continue
				}

				// 从设备组获取关联负载信息
				var foundSpeaker, foundLight bool
				var speakerSn, lightSn string

				// 查找无人机对应的喊话器和照明灯
				for _, sp := range SpeakerGroups {
					if sp.droenSn == drone.VehicleId {
						speakerSn = sp.payloadSn
						foundSpeaker = true
						break
					}
				}

				for _, lt := range LightGroups {
					if lt.droenSn == drone.VehicleId {
						lightSn = lt.payloadSn
						foundLight = true
						break
					}
				}

				// 清除相关设备数据
				if foundSpeaker {
					payloadRedisSvc.delSpeakerText(speakerSn)
					payloadRedisSvc.delPayloadDrcData(speakerSn)
				}

				if foundLight {
					payloadRedisSvc.delPayloadDrcData(lightSn)
				}

				droneSvc.droneOffline(drone, gateway.DeviceSN)
			}

			s.deviceOffline(deviceSn)
		}
	}
}

// refreshDockLiveStream 刷新机场直播
func (s *DeviceService) refreshDockLiveStream() {
	// 目前只需要刷新 机场1、机场3
	var dockList []*model.Device
	//err := s.db.Model(model.Device{}).Where("domain = ?", cloud_enum.Dock.Value()).Where("device_type = ? or device_type = ?", cloud_enum.Device_DOCK.Value(), cloud_enum.Device_DOCK3.Value()).Find(&dockList).Error
	err := s.db.Model(model.Device{}).Where("device_type = ? or device_type = ?", cloud_enum.Device_DOCK.Value(), cloud_enum.Device_DOCK3.Value()).Find(&dockList).Error
	if err != nil {
		return
	}

	for _, dock := range dockList {
		err = s.deviceRedisService.DelDeviceLiveStreaming(dock.DeviceSN)
		if err != nil {
			s.logger.Errorf("refreshDockLiveStream: DelDeviceLiveStreaming failed, err: %v", err)
			continue
		}

		err = s.liveStreamService.StartDockLiveStream(dock)
		if err != nil {
			s.logger.Errorf("refreshDockLiveStream: StartDockLiveStream failed, err: %v", err)
			continue
		}
	}
}
