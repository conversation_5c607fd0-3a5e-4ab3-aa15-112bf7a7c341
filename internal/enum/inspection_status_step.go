package enum

type InspectionStatusStepEnum int

const (
	InspectionStatusStepEnum_Before InspectionStatusStepEnum = iota + 1 // before
	InspectionStatusStepEnum_Doing                                      // doing
	InspectionStatusStepEnum_After                                      // after
)

func (d InspectionStatusStepEnum) Value() int {
	return int(d)
}

func (d InspectionStatusStepEnum) String() string {
	switch d {
	case InspectionStatusStepEnum_Before:
		return "before"
	case InspectionStatusStepEnum_Doing:
		return "doing"
	case InspectionStatusStepEnum_After:
		return "after"
	default:
		return "unknown"
	}
}
