package enum

type InspectionStatusEnum int

const (
	InspectionStatusEnum_init          InspectionStatusEnum = iota + 1 // 任务初始化
	InspectionStatusEnum_Waiting                                       // 执行中-waiting
	InspectionStatusEnum_Wayline                                       // 执行中-航线飞行
	InspectionStatusEnum_AIControl                                     // 执行中-算法智能巡检
	InspectionStatusEnum_ReturnHome                                    // 安全返航
	InspectionStatusEnum_ManualControl                                 // 执行中-预设指令巡检
)

func (d InspectionStatusEnum) Value() int {
	return int(d)
}

func (d InspectionStatusEnum) String() string {
	switch d {
	case InspectionStatusEnum_init:
		return "任务初始化"
	case InspectionStatusEnum_Waiting:
		return "执行中-waiting"
	case InspectionStatusEnum_Wayline:
		return "执行中-航线飞行"
	case InspectionStatusEnum_AIControl:
		return "执行中-算法智能巡检"
	case InspectionStatusEnum_ReturnHome:
		return "安全返航"
	case InspectionStatusEnum_ManualControl:
		return "执行中-预设指令巡检"
	default:
		return "未知"
	}
}
