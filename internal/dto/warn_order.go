package dto

import "github.com/qxsugar/pkg/dbx"

type ListWorkOrderReq struct {
	Offset    int   `json:"offset" form:"offset,default=0"`
	Limit     int   `json:"limit" form:"limit,default=30"`
	StartTime int64 `json:"start_time" form:"start_time"`
	EndTime   int64 `json:"end_time" form:"end_time"`
	Level     int   `json:"level" form:"level"`
	Status    int   `json:"status" form:"status"`
	JobWarnID int   `json:"job_warn_id" form:"job_warn_id"`
}

type WorkOrderResp struct {
	JobWarnID             int               `json:"job_warn_id"`
	CompanyID             int               `json:"company_id"`
	JobRecordID           int               `json:"job_record_id"`            // 所属任务id
	JobRecordName         string            `json:"job_record_name"`          // 所属任务名称
	WarnType              []string          `json:"warn_type"`                // 事项
	PositionList          []JobWarnPosition `json:"position_list"`            // 事发地
	Level                 int               `json:"level"`                    // 优先级
	Status                int               `json:"status"`                   // 状态 1-已复核 2-派遣中 3-已忽略 4-复核中
	UserID                int               `json:"user_id"`                  // 处理人员id
	UserNickName          string            `json:"user_nick_name"`           // 处理人员名称
	HandleAt              dbx.TimeStamp     `json:"handle_at"`                // 发布时间
	WarnRecordCount       int               `json:"warn_record_count"`        // 告警记录数量
	WarnRecordHandleCount int               `json:"warn_record_handle_count"` // 已处理告警记录数量
	WarnAt                dbx.TimeStamp     `json:"warn_at"`                  // 事发时间
}

type WorkOrderDetailReq struct {
	JobWarnID int `json:"job_warn_id" form:"job_warn_id"`
}

type WorkOrderHandleDetailResp struct {
	JobWarnRecordID int                   `json:"job_warn_record_id"`
	Level           int                   `json:"level"`
	WarnDesc        string                `json:"warn_desc"`     // 目标
	PicList         []string              `json:"pic_list"`      // 告警图片
	Position        JobWarnRecordPosition `json:"position"`      // 位置
	WarnAt          dbx.TimeStamp         `json:"warn_at"`       // 事发时间
	Status          int                   `json:"status"`        // 复核状态 1-已复核 2-未复核 3-已忽略
	HandleAt        dbx.TimeStamp         `json:"handle_at"`     // 复核时间
	HandleResult    string                `json:"handle_result"` // 复核意见
	UrlList         []string              `json:"url_list"`      // 复核附件
}

type WorkOrderDeleteReq struct {
	JobWarnID int `json:"job_warn_id" form:"job_warn_id"`
}
