package dto

import (
	"wukong-api/cloud_sdk/cloud_api/media/request"
)

type StartRealTimeReq struct {
	PullStreamUrl   string   `json:"pull_stream_url"`
	PushStreamUrl   string   `json:"push_stream_url"`
	JobID           string   `json:"job_id"`
	TargetClasses   []string `json:"target_classes"`
	DroneSn         string   `json:"drone_sn"`
	DockSn          string   `json:"dock_sn"`
	ObjectKeyPrefix string   `json:"object_key_prefix"`
}

type StartNoRealTimeReq struct {
	// MediaType       int         `json:"media_type"` // 1-视频 2-图片
	JobRecordID     int         `json:"job_record_id"`
	TargetClasses   []string    `json:"target_classes"`
	ObjectKeyPrefix string      `json:"object_key_prefix"`
	AttitudeKey     string      `json:"attitude_key"`
	Data            interface{} `json:"data"`
}

type VideoDataReq struct {
	VideoURL      string `json:"video_url"`
	LiveStartTime int    `json:"live_start_time"`
	FileID        string `json:"file_id"`
}

type PicDataReq struct {
	PicObjectKeyList []string `json:"pic_object_key_list"`
}

type NoRealTimeVideoResp struct {
	VideoObjectKey string     `json:"video_object_key"`
	FileID         string     `json:"file_id"`
	JobRecordID    int        `json:"job_record_id"`
	Alarm          []AlarmDto `json:"alarm"`
}

type AlarmDto struct {
	TimeSec         float64  `json:"time_sec"`
	ObjectTypeList  []string `json:"object_type_list"`
	PicObjectKey    string   `json:"pic_object_key"`
	AlgorithmSource int      `json:"algorithm_source" default:"0"` // 算法来源 0-yolo 1-大模型
	ModelCheck      string   `json:"model_check"`                  // 模型分析
}

type NoRealTimePicResp struct {
	JobRecordID int           `json:"job_record_id"`
	DataList    []PicDataResp `json:"data_list"`
}

type PicDataResp struct {
	OutputObjectKey string   `json:"output_object_key"`
	ObjectTypeList  []string `json:"object_type_list"`
	ExifData        ExifData `json:"exif_data"`
}

type ExifData struct {
	DateTimeOriginal  int     `json:"DateTimeOriginal"`
	LRFTargetDistance float64 `json:"LRFTargetDistance"`
	LRFTargetLon      float64 `json:"LRFTargetLon"`
	LRFTargetLat      float64 `json:"LRFTargetLat"`
	LRFTargetAbsAlt   float64 `json:"LRFTargetAbsAlt"`
}

type RealTimeResp struct {
	JobID          string   `json:"job_id"`
	DroneSn        string   `json:"drone_sn"`
	ObjectTypeList []string `json:"object_type_list"`
	CurrentSec     float64  `json:"current_sec"`
	DockSn         string   `json:"dock_sn"`
	PicObjectKey   string   `json:"pic_object_key"`
}

type Trajectory struct {
	Bd09ll    request.Position `json:"bd09ll"`   // 百度经纬度坐标（bd09ll）
	Standard  request.Position `json:"standard"` // GPS标准坐标
	Timestamp int64            `json:"timestamp"`
}

type JobTrajectoryResp struct {
	Type             int          `json:"type"`              // 任务类型 1-手动 2-自动
	TaskTrajectory   []Trajectory `json:"task_trajectory"`   // 任务轨迹
	TargetTrajectory []KMLPoint   `json:"target_trajectory"` // 目标航线轨迹
}

// StartRealTimeControlReq 开启实时控制连接请求
type StartRealTimeControlReq struct {
	PullStreamUrl string   `json:"pull_stream_url"`
	PushStreamUrl string   `json:"push_stream_url"`
	JobID         string   `json:"job_id"`
	TargetClasses []string `json:"target_classes"`
	DroneSn       string   `json:"drone_sn"`
	DockSn        string   `json:"dock_sn"`
}

// RealTimeControlReq 实时控制请求，请求算法做控制决策
type RealTimeControlReq struct {
	DepthControlEnabled bool `json:"depth_control_enabled"`
}

// RealTimeControlResp 控制决策
type RealTimeControlResp struct {
	JobID   string `json:"job_id"`
	DockSn  string `json:"dock_sn"`
	DroneSn string `json:"drone_sn"`
	Type    int    `json:"type"` // 1-完成单次绕飞巡检
}
