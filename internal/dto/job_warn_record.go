package dto

import (
	"wukong-api/cloud_sdk/cloud_api/media/request"

	"github.com/qxsugar/pkg/dbx"
)

type JobWarnRecordResp struct {
	ID              int                   `json:"id"`
	CompanyID       int                   `json:"company_id"`
	JobRecordID     int                   `json:"job_record_id"`
	JobRecordName   string                `json:"job_record_name"`
	WarnDesc        string                `json:"warn_desc"`
	PicList         []string              `json:"pic_list"`
	Position        JobWarnRecordPosition `json:"position"`
	Level           int                   `json:"level"`
	WarnType        []string              `json:"warn_type"`
	Status          int                   `json:"status"`
	WarnAt          dbx.TimeStamp         `json:"warn_at"`
	HandleAt        dbx.TimeStamp         `json:"handle_at"`
	CreatedAt       dbx.TimeStamp         `json:"created_at"`
	UpdatedAt       dbx.TimeStamp         `json:"updated_at"`
	DroneSN         string                `json:"drone_sn"`
	JobWarnID       int                   `json:"job_warn_id"`
	AlgorithmSource int                   `json:"algorithm_source" default:"0"` // 算法来源 0-yolo 1-大模型
}

type JobWarnRecordDto struct {
	//ID               int                   `json:"id"`
	CompanyID        int                   `json:"company_id"`
	JobRecordID      int                   `json:"job_record_id"`
	JobRecordName    string                `json:"job_record_name"`
	WarnDesc         string                `json:"warn_desc"`
	PicObjectKeyList []string              `json:"pic_object_key_list"`
	Position         JobWarnRecordPosition `json:"position"`
	Level            int                   `json:"level"`
	WarnType         []string              `json:"warn_type"`
	Status           int                   `json:"status"`
	WarnAt           dbx.TimeStamp         `json:"warn_at"`
	DroneSN          string                `json:"drone_sn"`
	JobWarnID        int                   `json:"job_warn_id"`
	AlgorithmSource  int                   `json:"algorithm_source" default:"0"` // 算法来源 0-yolo 1-大模型
	ModelCheck       string                `json:"model_check"`                  // 模型分析
}

type JobWarnRecordPosition struct {
	Bd09ll   request.Position `json:"bd09ll"`   // 百度经纬度坐标（bd09ll）
	Standard request.Position `json:"standard"` // GPS标准坐标
}

type JobWarnRecordListReq struct {
	Offset      int    `json:"offset" form:"offset,default=0"`
	Limit       int    `json:"limit" form:"limit,default=30"`
	StartTime   int64  `json:"start_time" form:"start_time"`
	EndTime     int64  `json:"end_time" form:"end_time"`
	WarnType    string `json:"warn_type" form:"warn_type"`
	Level       int    `json:"level" form:"level"`
	Status      int    `json:"status" form:"status"`
	Name        string `json:"name" form:"name"`
	ID          int    `json:"id" form:"id"`
	JobRecordID int    `json:"job_record_id" form:"job_record_id"`
}

type JobWarnRecordUpdateReq struct {
	ID     int `json:"id" binding:"required"`
	Status int `json:"status"`
}

type JobWarnRecordOverviewResp struct {
	WarnStatistics WarnStatistics `json:"warn_statistics"`
	TypeCount      []TypeCount    `json:"type_count"`
}

type WarnStatistics struct {
	TotalCount      int64   `json:"total_count"`       // 总告警数
	ProcessedCount  int64   `json:"processed_count"`   // 已处理告警数
	UnProcessCount  int64   `json:"un_process_count"`  // 未处理告警数
	IgnoredCount    int64   `json:"ignored_count"`     // 已忽略告警数
	ProcessingCount int64   `json:"processing_count"`  // 处理中告警数
	AvgProcessHours float64 `json:"avg_process_hours"` // 平均处理时长(小时)
}

type TypeCount struct {
	WarnType string `json:"warn_type"`
	Count    int    `json:"count"`
}
