package controller

type Registry struct {
	MemberApi                 *MemberApi
	LoginApi                  *LoginApi
	CompanyApi                *CompanyApi
	CommonApi                 *CommonApi
	UserApi                   *UserApi
	IsvApi                    *IsvApi
	IsvUserApi                *IsvUserApi
	DroneApi                  DroneInterface
	AdminCompanyApi           *AdminCompanyApi
	AdminUserApi              *AdminUserApi
	CommonFileMgrApi          CommonFileMgrInterface
	WebSocketApi              WebSocketInterface
	WaylineFileApi            WaylineFileInterface
	LiveCallbackApi           *LiveCallbackApi
	OssApi                    *OssApi
	AlgorithmModelApi         AlgorithmModelInterface
	AlgorithmModelScenarioApi AlgorithmModelScenarioInterface
	InspectionJobApi          *InspectionJobApi
	InspectionJobRecordApi    *InspectionJobRecordApi
	WaylineManageApi          *WaylineManageApi
	DeviceApi                 *DeviceApi
	DronePilotApi             *DronePilotApi
	VisualizationApi          *VisualizationApi
	DockDebugApi              *DockDebugApi
	WaylineJobApi             *WaylineJobApi
	LiveStreamApi             *LiveStreamApi
	DockDRCApi                *DockDRCApi
	DockControlApi            *DockControlApi
	CockpitFlightApi          *CockpitApi
	WorkBenchApi              *WorkBenchApi
	FenceApi                  *FenceApi
	JobWarnRecordApi          *JobWarnRecordApi
	JobWarnApi                *JobWarnApi
	JobWarnDispatchApi        *JobWarnDispatchApi
	JobWarnRecordHandleApi    *JobWarnRecordHandleApi
	DockPsdkApi               *DockPsdkApi
	DockRemoteControlApi      *DockRemoteControlApi
	PayloadApi                *PayloadApi
	WarnWorkOrderApi          *WarnWorkOrderApi
	UndergroundInfoApi        *UndergroundInfoApi
	DeviceManageApi           *DeviceManageApi
	SmsApi                    *SmsApi
	CameraApi                 *CameraApi
}

func NewRegister() *Registry {
	return &Registry{
		MemberApi:                 NewMemberApi(),
		LoginApi:                  NewLoginApi(),
		UserApi:                   NewUserApi(),
		CommonApi:                 NewCommonApi(),
		CompanyApi:                NewCompanyApi(),
		IsvApi:                    NewIsvApi(),
		IsvUserApi:                NewIsvUserApi(),
		DroneApi:                  NewDroneApi(),
		AdminUserApi:              NewAdminUserApi(),
		AdminCompanyApi:           NewAdminCompanyApi(),
		CommonFileMgrApi:          NewCommonFileMgrApi(),
		WebSocketApi:              NewWebSocketApi(),
		WaylineFileApi:            NewWaylineFileApi(),
		LiveCallbackApi:           NewLiveCallbackApi(),
		OssApi:                    NewOssApi(),
		AlgorithmModelApi:         NewAlgorithmModelApi(),
		AlgorithmModelScenarioApi: NewAlgorithmModelScenarioApi(),
		InspectionJobApi:          NewInspectionJobApi(),
		InspectionJobRecordApi:    NewInspectionJobRecordApi(),
		WaylineManageApi:          NewWaylineManageApi(),
		DeviceApi:                 NewDeviceApi(),
		DronePilotApi:             NewDronePilotApi(),
		VisualizationApi:          NewVisualizationApi(),
		DockDebugApi:              NewDockDebugApi(),
		WaylineJobApi:             NewWaylineJobApi(),
		LiveStreamApi:             NewLiveStreamApi(),
		DockDRCApi:                NewDockDRCApi(),
		DockControlApi:            NewDockControlApi(),
		CockpitFlightApi:          NewCockpitApi(),
		WorkBenchApi:              NewWorkBenchApi(),
		FenceApi:                  NewFenceApi(),
		JobWarnRecordApi:          NewJobWarnRecordApi(),
		JobWarnApi:                NewJobWarnApi(),
		JobWarnDispatchApi:        NewJobWarnDispatchApi(),
		JobWarnRecordHandleApi:    NewJobWarnRecordHandleApi(),
		DockPsdkApi:               NewDockPsdkApi(),
		DockRemoteControlApi:      NewDockRemoteControlApi(),
		PayloadApi:                NewPayloadApi(),
		WarnWorkOrderApi:          NewWarnWorkOrderApi(),
		UndergroundInfoApi:        NewUndergroundInfoApi(),
		DeviceManageApi:           NewDeviceManageApi(),
		SmsApi:                    NewSmsApi(),
		CameraApi:                 NewCameraApi(),
	}
}
