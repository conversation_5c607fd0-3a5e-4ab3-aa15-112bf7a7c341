package controller

import (
	"wukong-api/internal/dto"
	"wukong-api/internal/repo"
	"wukong-api/internal/service"
	"wukong-api/pkg/token"

	"github.com/gin-gonic/gin"
	"github.com/qxsugar/pkg/apix"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type InspectionJobApi struct {
	db                   *gorm.DB
	logger               *zap.SugaredLogger
	inspectionJobService *service.InspectionJobService
}

func NewInspectionJobApi() *InspectionJobApi {
	return &InspectionJobApi{
		db:                   repo.GetDatabase(),
		logger:               repo.GetLogger(),
		inspectionJobService: service.NewInspectionJobService(),
	}
}

func (s *InspectionJobApi) Create(ctx *gin.Context) (interface{}, error) {
	reqBody := &dto.InspectionJobCreateReq{}
	err := ctx.ShouldBindJSON(&reqBody)
	if err != nil {
		return nil, apix.NewInvalidArgumentError().WithErr(err)
	}

	cid := ctx.GetInt(token.Cid)
	userId := ctx.GetInt(token.Userid)

	isAdmin, _ := service.NewCompanyUserService().IsAdmin(userId)
	if !isAdmin {
		return nil, apix.NewInternalError().WithMsg("该用户无权限")
	}

	if service.CheckIsOccupiedPermissions() {
		return nil, apix.NewInternalError().WithMsg("权限被占用，请联系管理员")
	}

	resp, err, errMsg := s.inspectionJobService.Create(reqBody, cid, userId)
	if err != nil {
		return nil, apix.NewInternalError().WithErr(err).WithMsg(errMsg)
	}

	return resp, nil
}

func (s *InspectionJobApi) OneClickTakeoff(ctx *gin.Context) (interface{}, error) {
	reqBody := &dto.InspectionJobOneClickTakeoffReq{}
	err := ctx.ShouldBindJSON(&reqBody)
	if err != nil {
		return nil, apix.NewInvalidArgumentError().WithErr(err)
	}

	cid := ctx.GetInt(token.Cid)
	userId := ctx.GetInt(token.Userid)

	isAdmin, _ := service.NewCompanyUserService().IsAdmin(userId)
	if !isAdmin {
		return nil, apix.NewInternalError().WithMsg("该用户无权限")
	}

	if service.CheckIsOccupiedPermissions() {
		return nil, apix.NewInternalError().WithMsg("权限被占用，请联系管理员")
	}
	resp, err, errMsg := s.inspectionJobService.OneClickTakeoff(reqBody, cid, userId)
	if err != nil {
		return nil, apix.NewInternalError().WithErr(err).WithMsg(errMsg)
	}
	return resp, nil
}

func (s *InspectionJobApi) ExecuteTemporaryTask(ctx *gin.Context) (interface{}, error) {
	reqBody := &dto.TemporaryTasksReq{}
	err := ctx.ShouldBindJSON(&reqBody)
	if err != nil {
		return nil, apix.NewInvalidArgumentError().WithErr(err)
	}

	cid := ctx.GetInt(token.Cid)
	userId := ctx.GetInt(token.Userid)

	isAdmin, _ := service.NewCompanyUserService().IsAdmin(userId)
	if !isAdmin {
		return nil, apix.NewInternalError().WithMsg("该用户无权限")
	}

	if service.CheckIsOccupiedPermissions() {
		return nil, apix.NewInternalError().WithMsg("权限被占用，请联系管理员")
	}

	err, errMsg := s.inspectionJobService.ExecuteTemporaryTask(reqBody, cid, userId)
	if err != nil {
		return nil, apix.NewInternalError().WithErr(err).WithMsg(errMsg)
	}

	return "ok", nil
}

// ExecuteEmergencyTask 执行“0514叫应航线”
func (s *InspectionJobApi) ExecuteEmergencyTask(ctx *gin.Context) (interface{}, error) {
	err, errMsg := s.inspectionJobService.ExecuteEmergencyTask()
	if err != nil {
		return nil, apix.NewInternalError().WithErr(err).WithMsg(errMsg)
	}

	return "ok", nil
}

func (s *InspectionJobApi) List(ctx *gin.Context) (interface{}, error) {
	reqBody := &dto.InspectionJobListReq{}
	err := ctx.ShouldBindQuery(&reqBody)
	if err != nil {
		return nil, apix.NewInvalidArgumentError()
	}

	cid := ctx.GetInt(token.Cid)

	res, err := s.inspectionJobService.List(reqBody, cid)
	if err != nil {
		return nil, apix.NewInternalError().WithErr(err)
	}

	return res, nil
}

func (s *InspectionJobApi) Detail(ctx *gin.Context) (interface{}, error) {
	reqBody := &dto.InspectionJobIdReq{}
	err := ctx.ShouldBind(&reqBody)
	if err != nil {
		return nil, apix.NewInvalidArgumentError()
	}

	res, err := s.inspectionJobService.Detail(reqBody)
	if err != nil {
		return nil, apix.NewInternalError().WithErr(err)
	}

	return res, nil
}

func (s *InspectionJobApi) Update(ctx *gin.Context) (interface{}, error) {
	reqBody := &dto.InspectionJobUpdateReq{}
	err := ctx.ShouldBindJSON(&reqBody)
	if err != nil {
		return nil, apix.NewInvalidArgumentError().WithErr(err)
	}

	err, errMsg := s.inspectionJobService.Update(reqBody)
	if err != nil {
		return nil, apix.NewInternalError().WithErr(err).WithMsg(errMsg)
	}

	return "ok", nil
}

func (s *InspectionJobApi) Delete(ctx *gin.Context) (interface{}, error) {
	reqBody := &dto.InspectionJobIdReq{}
	err := ctx.ShouldBindJSON(&reqBody)
	if err != nil {
		return nil, apix.NewInvalidArgumentError().WithErr(err)
	}

	err = s.inspectionJobService.Delete(reqBody)
	if err != nil {
		return nil, apix.NewInternalError().WithErr(err)
	}

	return "ok", nil
}

func (s *InspectionJobApi) Execute(ctx *gin.Context) (interface{}, error) {
	req := struct {
		Id int `binding:"required" json:"id"`
	}{}

	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		return nil, apix.NewInvalidArgumentError().WithErr(err)
	}

	userId := ctx.GetInt(token.Userid)

	isAdmin, _ := service.NewCompanyUserService().IsAdmin(userId)
	if !isAdmin {
		return nil, apix.NewInternalError().WithMsg("该用户无权限")
	}

	if service.CheckIsOccupiedPermissions() {
		return nil, apix.NewInternalError().WithMsg("权限被占用，请联系管理员")
	}

	err, errMsg := s.inspectionJobService.ImmediateExecute(req.Id, userId)
	if err != nil {
		return nil, apix.NewInternalError().WithErr(err).WithMsg(errMsg)
	}

	return "ok", nil
}

func (s *InspectionJobApi) JobCalendar(ctx *gin.Context) (interface{}, error) {
	req := struct {
		DateTimestamp int64 `binding:"required" json:"dateTimestamp" form:"dateTimestamp"`
	}{}

	err := ctx.ShouldBind(&req)
	if err != nil {
		return nil, apix.NewInvalidArgumentError().WithErr(err)
	}

	cid := ctx.GetInt(token.Cid)

	resp, err := s.inspectionJobService.JobCalendar(req.DateTimestamp, cid)
	if err != nil {
		return nil, apix.NewInternalError().WithErr(err)
	}

	return resp, nil
}

func (s *InspectionJobApi) CreateJobWithFacility(ctx *gin.Context) (interface{}, error) {
	reqBody := &dto.CreateJobWithFacilityReq{}
	err := ctx.ShouldBindJSON(&reqBody)
	if err != nil {
		return nil, apix.NewInvalidArgumentError().WithErr(err)
	}

	cid := ctx.GetInt(token.Cid)
	userId := ctx.GetInt(token.Userid)

	isAdmin, _ := service.NewCompanyUserService().IsAdmin(userId)
	if !isAdmin {
		return nil, apix.NewInternalError().WithMsg("该用户无权限")
	}

	if service.CheckIsOccupiedPermissions() {
		return nil, apix.NewInternalError().WithMsg("权限被占用，请联系管理员")
	}

	err = s.inspectionJobService.CreateJobWithFacility(reqBody, cid, userId)
	if err != nil {
		return nil, apix.NewInternalError().WithErr(err)
	}

	return "ok", nil
}
