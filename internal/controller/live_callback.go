package controller

import (
	"bytes"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"strings"
	"wukong-api/internal/dto"
	"wukong-api/internal/model"
	"wukong-api/internal/repo"
	"wukong-api/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/qxsugar/pkg/apix"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type LiveCallbackApi struct {
	logger              *zap.SugaredLogger
	db                  *gorm.DB
	liveCallbackService *service.LiveCallbackService
}

func NewLiveCallbackApi() *LiveCallbackApi {
	return &LiveCallbackApi{
		logger:              repo.GetLogger(),
		db:                  repo.GetDatabase(),
		liveCallbackService: service.NewLiveCallbackService(),
	}
}

// OnDvr 当DVR录制关闭一个flv文件时触发的回调
func (c *LiveCallbackApi) OnDvr(ctx *gin.Context) {
	reqData := struct {
		Action   string `json:"action"` // "action":"on_dvr"
		ClientId string `json:"client_id"`
		Ip       string `json:"ip"`
		Vhost    string `json:"vhost"`
		App      string `json:"app"`    // "app":"live"
		Stream   string `json:"stream"` // "stream":"0000-0002"
		Cwd      string `json:"cwd"`    // "cwd":"/usr/local/srs-cloud/platform"
		File     string `json:"file"`   // "file":"./objs/nginx/html/live/0000-0002.1691463796006.flv"
	}{}

	err := ctx.ShouldBindJSON(&reqData)
	if err != nil {
		c.logger.Errorf("OnDvr callback params err:%v", err)
		ctx.JSON(http.StatusOK, dto.SrsResponseResult{
			Code: -1,
		})
		return
	}

	// 拼接下载地址
	prefix := "./objs/nginx/html/"
	var fileName string
	if strings.HasPrefix(reqData.File, prefix) {
		fileName = reqData.File[len(prefix):]
	}
	url := "http://*************:8080/" + fileName

	// 根据 sn查询 cid
	drone := model.Drone{}
	err = c.db.Model(drone).Where("vehicle_id = ?", reqData.Stream).First(&drone).Error
	if err != nil {
		c.logger.Errorf("无人机不存在: %s err: %s", reqData.Stream, err.Error())
		ctx.JSON(http.StatusOK, dto.SrsResponseResult{
			Code: -1,
		})
		return
	}

	// 上传cos
	extInfo := service.ExtInfo{
		JobId: drone.ManualJobId, // fixme 此处的的taskId可能为-1，stream应该绑定taskId，
	}

	var cid int
	//cid := drone.CompanyId

	result, err := service.NewCommonFileMgrService().UploadFileByUrl(ctx, url, service.TaskRecordLive, cid, extInfo)
	if err != nil {
		c.logger.Errorf("on dvr callback upload file fail: %s err: %s", fileName, err.Error())
		ctx.JSON(http.StatusOK, dto.SrsResponseResult{
			Code: -1,
		})
		return
	}

	// 如果无人机目前有任务就入库
	if drone.ManualJobId != "" {
		var job *model.ManualJob
		job, err = service.NewManualJobService().GetManualJobByJobId(drone.ManualJobId)
		if err != nil {
			return
		}

		if err != nil {
			c.logger.Error("无人机任务记录视频地址失败 fileName: %s err: %s", fileName, err.Error())
			ctx.JSON(http.StatusOK, dto.SrsResponseResult{
				Code: -1,
			})
			return
		}

		// fixme
		jobVideo := model.MediaFile{
			FileID:       uuid.New().String(),
			FileName:     result.Name,
			CompanyID:    job.CompanyId,
			ObjectKey:    result.ObjectKey,
			IsOriginal:   false,
			Drone:        "",
			ManualJobId:  "",
			WaylineJobID: "",
			FileType:     2,
		}

		err = c.db.Create(&jobVideo).Error
		if err != nil {
			c.logger.Error("无人机任务记录视频地址失败 fileName: %s, 保存巡检任务失败 err: %s", fileName, err.Error())
			ctx.JSON(http.StatusOK, dto.SrsResponseResult{
				Code: -1,
			})
			return
		}
	}

	// 调用删除接口
	jsonStr, err := json.Marshal(reqData)
	if err != nil {
		c.logger.Error("远程调用删除接口失败 fileName: %s err: %s", fileName, err.Error())
		ctx.JSON(http.StatusOK, dto.SrsResponseResult{
			Code: -1,
		})
		return
	}
	reqUrl := "http://*************:8001/api/v1/dvrs/delete_local"
	req, err := http.NewRequest("POST", reqUrl, bytes.NewBuffer(jsonStr))
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	response, err := client.Do(req)
	if err != nil {
		c.logger.Error("远程调用删除接口失败 fileName: %s err: %s", fileName, err.Error())
		ctx.JSON(http.StatusOK, dto.SrsResponseResult{
			Code: -1,
		})
		return
	}
	defer response.Body.Close()

	if response.StatusCode != 200 {
		c.logger.Error("远程调用删除接口失败 fileName: %s err: %s", fileName, err.Error())
		ctx.JSON(http.StatusOK, dto.SrsResponseResult{
			Code: -1,
		})
		return
	}

	body, _ := ioutil.ReadAll(response.Body)
	var responseData dto.SrsResponseResult
	err = json.Unmarshal(body, &responseData)
	if err != nil || responseData.Code != 0 {
		c.logger.Error("远程调用删除接口失败 fileName: %s err: %s", fileName, err.Error())
		ctx.JSON(http.StatusOK, dto.SrsResponseResult{
			Code: -1,
		})
		return
	}

	ctx.JSON(http.StatusOK, dto.SrsResponseResult{
		Code: 0,
	})

}

// LiveRecord aliyun视频直播-文件生成回调
func (c *LiveCallbackApi) LiveRecord(ctx *gin.Context) (interface{}, error) {
	reqData := &dto.LiveRecordWithAliyunReq{}
	err := ctx.ShouldBindJSON(&reqData)
	if err != nil {
		return nil, apix.NewInvalidArgumentError().WithErr(err)
	}

	if reqData.Stream == "" || reqData.Uri == "" {
		c.logger.Warnf("LiveRecord: {app:%s domain:%s duration:%.3f record_id:%s stream:%s uri:%s start_time:%d stop_time:%d}",
			reqData.App,
			reqData.Domain,
			reqData.Duration,
			reqData.RecordId,
			reqData.Stream,
			reqData.Uri,
			reqData.StartTime,
			reqData.StopTime)

		return nil, apix.NewInvalidArgumentError().WithErr(err)
	}

	//c.logger.Infof("LiveRecord: {app:%s domain:%s duration:%.3f record_id:%s stream:%s uri:%s start_time:%d stop_time:%d}",
	//	reqData.App,
	//	reqData.Domain,
	//	reqData.Duration,
	//	reqData.RecordId,
	//	reqData.Stream,
	//	reqData.Uri,
	//	reqData.StartTime,
	//	reqData.StopTime)

	err = c.liveCallbackService.LiveRecordWithAliyun(ctx, *reqData)
	if err != nil {
		return nil, apix.NewInternalError().WithErr(err)
	}

	return nil, nil
}
