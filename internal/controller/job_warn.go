package controller

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"wukong-api/internal/dto"
	"wukong-api/internal/repo"
	"wukong-api/internal/service"
	"wukong-api/pkg/token"

	"github.com/gin-gonic/gin"
	"github.com/qxsugar/pkg/apix"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type JobWarnApi struct {
	db      *gorm.DB
	logger  *zap.SugaredLogger
	service *service.JobWarnService
}

func NewJobWarnApi() *JobWarnApi {
	return &JobWarnApi{
		db:      repo.GetDatabase(),
		logger:  repo.GetLogger(),
		service: service.NewJobWarnService(),
	}
}

func (s *JobWarnApi) List(ctx *gin.Context) (interface{}, error) {
	reqBody := &dto.JobWarnListReq{}
	err := ctx.ShouldBindQuery(&reqBody)
	if err != nil {
		return nil, apix.NewInvalidArgumentError()
	}

	cid := ctx.GetInt(token.Cid)

	res, err := s.service.List(*reqBody, cid, false)
	if err != nil {
		return nil, apix.NewInternalError().WithErr(err)
	}

	return res, nil
}
func (s *JobWarnApi) ModelCheck(ctx *gin.Context) (interface{}, error) {
	reqBody := &dto.JobWarnModelCheckReq{}
	err := ctx.ShouldBindJSON(reqBody)
	if err != nil {
		return nil, apix.NewInvalidArgumentError().WithErr(err)
	}
	cid := ctx.GetInt(token.Cid)

	res, err := s.service.ModelCheck(*reqBody, cid)
	if err != nil {
		return nil, apix.NewInternalError().WithErr(err).WithMsg(err.Error())
	}

	return res, nil
}

func (s *JobWarnApi) Overview(ctx *gin.Context) (interface{}, error) {
	cid := ctx.GetInt(token.Cid)

	res, err := s.service.Overview(cid)
	if err != nil {
		return nil, apix.NewInternalError().WithErr(err)
	}

	return res, nil
}

// GetJobWarnsByAdmin 移动端管理人员获取告警列表
func (s *JobWarnApi) ListByAdmin(ctx *gin.Context) (interface{}, error) {
	reqBody := &dto.JobWarnByAdminReq{}
	err := ctx.ShouldBindQuery(reqBody)
	if err != nil {
		return nil, apix.NewInvalidArgumentError().WithErr(err)
	}

	cid := ctx.GetInt(token.Cid)

	// 调用服务层方法获取告警列表
	res, err := s.service.ListByAdmin(*reqBody, cid)
	if err != nil {
		return nil, apix.NewInternalError().WithErr(err)
	}

	return res, nil
}

// GetJobWarnsByUser 移动端处理人员获取告警列表
func (s *JobWarnApi) ListByUser(ctx *gin.Context) (interface{}, error) {
	reqBody := &dto.JobWarnByUserReq{}
	err := ctx.ShouldBindQuery(reqBody)
	if err != nil {
		return nil, apix.NewInvalidArgumentError().WithErr(err)
	}

	uid := ctx.GetInt(token.Userid)

	// 调用服务层方法获取告警列表
	res, err := s.service.ListByUser(*reqBody, uid)
	if err != nil {
		return nil, apix.NewInternalError().WithErr(err)
	}

	return res, nil
}

// Sender 发送自定义json到企业微信webhook
func (s *JobWarnApi) Sender(ctx *gin.Context) (interface{}, error) {
	// 获取query参数key
	key := ctx.Query("key")
	if key == "" {
		return nil, apix.NewInvalidArgumentError().WithMsg("key不能为空")
	}

	// 读取请求体
	body, err := ctx.GetRawData()
	if err != nil {
		return nil, apix.NewInvalidArgumentError().WithMsg("读取body失败")
	}

	if len(body) == 0 {
		return nil, apix.NewInvalidArgumentError().WithMsg("body不能为空")
	}

	// 解析json字符串
	var jsonData interface{}
	err = json.Unmarshal(body, &jsonData)
	if err != nil {
		return nil, apix.NewInvalidArgumentError().WithMsg("body不是合法的json字符串")
	}

	// 构建企业微信webhook url
	url := "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=" + key

	// 发送请求
	resp, err := s.httpPostJson(url, jsonData)
	if err != nil {
		s.logger.Errorf("调用企业微信webhook失败: %v", err)
		return nil, apix.NewInternalError().WithMsg("调用企业微信webhook失败")
	}

	return resp, nil
}

// httpPostJson 发送POST请求，传递json数据
func (s *JobWarnApi) httpPostJson(url string, data interface{}) (map[string]interface{}, error) {
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBytes))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var result map[string]interface{}
	err = json.Unmarshal(respBody, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// updateStatus 更新状态
func (s *JobWarnApi) UpdateStatus(ctx *gin.Context) (interface{}, error) {
	reqBody := &dto.JobWarnUpdateStatusReq{}
	err := ctx.ShouldBindJSON(reqBody)
	if err != nil {
		return nil, apix.NewInvalidArgumentError().WithErr(err)
	}

	cid := ctx.GetInt(token.Cid)

	res, err := s.service.UpdateStatus(*reqBody, cid)
	if err != nil {
		return nil, apix.NewInternalError().WithErr(err)
	}

	return res, nil
}

// Delete 删除告警
func (s *JobWarnApi) Delete(ctx *gin.Context) (interface{}, error) {
	reqBody := &dto.JobWarnDeleteReq{}
	err := ctx.ShouldBindJSON(reqBody)
	if err != nil {
		return nil, apix.NewInvalidArgumentError().WithErr(err)
	}

	err = s.service.Delete(*reqBody)
	if err != nil {
		return nil, apix.NewInternalError().WithErr(err)
	}

	return "ok", nil
}
