package controller

import (
	"wukong-api/internal/repo"
	"wukong-api/internal/service"

	"wukong-api/pkg/utils"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type CameraApi struct {
	db      *gorm.DB
	logger  *zap.SugaredLogger
	service *service.DeviceManageService
}

func NewCameraApi() *CameraApi {
	return &CameraApi{
		db:      repo.GetDatabase(),
		logger:  repo.GetLogger(),
		service: service.NewDeviceManageService(),
	}
}

func (c *CameraApi) PresetPoint(ctx *gin.Context) (interface{}, error) {
	utils.PresetPoint()
	return nil, nil
}
