package model

import (
	"github.com/qxsugar/pkg/dbx"
	"gorm.io/plugin/soft_delete"
)

// JobWarnRecord 告警异常记录
type JobWarnRecord struct {
	ID              int                   `json:"id"`              // 告警异常记录
	CompanyID       int                   `json:"company_id"`      // 所属公司
	JobRecordID     int                   `json:"job_record_id"`   // 巡检任务记录id
	JobRecordName   string                `json:"job_record_name"` // 巡检任务名称
	WarnDesc        string                `json:"warn_desc"`       // 描述
	PicList         string                `json:"pic_list"`        // 图片
	Position        string                `json:"position"`        // 位置
	Level           int                   `json:"level"`           // 1-高 2-中 3-低
	WarnType        string                `json:"warn_type"`
	Status          int                   `json:"status"`                            // 1-已处理 2-未处理 3-已忽略
	WarnAt          dbx.TimeStamp         `json:"warn_at"`                           // 创建时间
	HandleAt        dbx.TimeStamp         `json:"handle_at"`                         // 处理时间
	CreatedAt       dbx.TimeStamp         `json:"created_at"`                        // 创建时间
	UpdatedAt       dbx.TimeStamp         `json:"updated_at"`                        // 更新时间
	IsDeleted       soft_delete.DeletedAt `json:"is_deleted" gorm:"softDelete:flag"` // 删除 0-没删除，1-已经删除
	DroneSN         string                `json:"drone_sn"`                          // 作业的无人机
	JobWarnID       int                   `json:"job_warn_id"`                       // 告警异常id
	ModelCheck      string                `json:"model_check"`                       // 大模型检查
	AlgorithmSource int                   `json:"algorithm_source"`                  // 算法来源 0-yolo 1-大模型
}

func (JobWarnRecord) TableName() string {
	return "job_warn_record"
}

type JobWarnRecordWithHandle struct {
	ID            int                   `json:"id"`              // 告警异常记录
	CompanyID     int                   `json:"company_id"`      // 所属公司
	JobRecordID   int                   `json:"job_record_id"`   // 巡检任务记录id
	JobRecordName string                `json:"job_record_name"` // 巡检任务名称
	WarnDesc      string                `json:"warn_desc"`       // 描述
	PicList       string                `json:"pic_list"`        // 图片
	Position      string                `json:"position"`        // 位置
	Level         int                   `json:"level"`           // 1-高 2-中 3-低
	WarnType      string                `json:"warn_type"`
	Status        int                   `json:"status"`                            // 1-已处理 2-未处理 3-已忽略
	WarnAt        dbx.TimeStamp         `json:"warn_at"`                           // 创建时间
	HandleAt      dbx.TimeStamp         `json:"handle_at"`                         // 处理时间
	CreatedAt     dbx.TimeStamp         `json:"created_at"`                        // 创建时间
	UpdatedAt     dbx.TimeStamp         `json:"updated_at"`                        // 更新时间
	IsDeleted     soft_delete.DeletedAt `json:"is_deleted" gorm:"softDelete:flag"` // 删除 0-没删除，1-已经删除
	DroneSN       string                `json:"drone_sn"`                          // 作业的无人机
	JobWarnID     int                   `json:"job_warn_id"`                       // 告警异常id
	HandleImages  string                `json:"handle_images"`                     // 处理图片
	HandleResult  string                `json:"handle_result"`                     // 处理结果
}
