package http

import (
	"wukong-api/internal/controller"
	"wukong-api/internal/middleware"
	"wukong-api/pkg/token"

	"github.com/gin-gonic/gin"
	"github.com/qxsugar/pkg/apix"
)

func registerPublicApi(router *gin.RouterGroup, registry *controller.Registry) {
	router.GET("/", apix.Wrapper(apix.Pong))
	router.POST("/v1/app/algorithm_model.invoke", apix.Wrapper(registry.AlgorithmModelApi.Invoke)) // 算法模型调用

	router.GET("/visualization.detail", apix.Wrapper(registry.VisualizationApi.Detail))
	router.POST("/visualization.update", apix.Wrapper(registry.VisualizationApi.Update))

	router.POST("/task_result.upload", apix.Wrapper(registry.CommonFileMgrApi.UploadTaskResult))

	router.GET("/v1/app/emergency_task.exec", apix.Wrapper(registry.InspectionJobApi.ExecuteEmergencyTask)) // 执行“0514叫应航线”

	// 通用功能接口
	router.POST("/common.capture_screenshot", apix.Wrapper(registry.CommonApi.CaptureScreenshot)) // 网页截图

	router.POST("/v1/app/sms.send", apix.Wrapper(registry.SmsApi.SMSUsage))
}

func registerAppApi(router *gin.RouterGroup, registry *controller.Registry) {
	// 不需要身份验证的平台接口
	router.POST("/v1/app/user.login", apix.Wrapper(registry.LoginApi.UserLogin))
	router.GET("/v1/app/company.config", apix.Wrapper(registry.CompanyApi.CompanyConfig))

	// 无人机的接口
	router.POST("/v1/app/drone.live_ready_notice", apix.Wrapper(registry.DroneApi.LiveReadyNotice)) // 无人机视频流ready通知
	router.POST("/v1/app/drone.taskId", apix.Wrapper(registry.DroneApi.GetDroneTaskId))             // 根据无人机sn获取对应任务id

	// websocket接口
	router.GET("/v1/app/websocket.start", apix.Wrapper(registry.WebSocketApi.StartWebSocket)) // 发起连接websocket

	// srs callback
	router.POST("/v1/dvrs", registry.LiveCallbackApi.OnDvr)

	// aliyun 文件生产回调
	router.POST("/v1/live_record", apix.Wrapper(registry.LiveCallbackApi.LiveRecord))

	// inspection_job_record
	router.POST("/v1/app/inspection_job_record.imagDown", registry.InspectionJobRecordApi.ImageDown)
	router.POST("/v1/app/inspection_job_record.get_trajectory", apix.Wrapper(registry.InspectionJobRecordApi.GetJobTrajectory))

	v1 := apix.NewRouterGroup(router.Group("/v1/app", middleware.AuthMiddleware(token.LoginRoleUser)))
	{
		// websocket
		v1.POST("/websocket.sub_service", registry.WebSocketApi.SubWebSocketService)    // 订阅业务功能
		v1.GET("/websocket.unsub_service", registry.WebSocketApi.UnSubWebSocketService) // 退订业务功能
		v1.POST("/websocket.update_object_key_manager", registry.WebSocketApi.UpdateObjectKeyManager)

		// 成员
		v1.GET("/member.list", registry.MemberApi.ListMember)
		v1.POST("/member.create", registry.MemberApi.AddMember)
		v1.POST("/member.delete", registry.MemberApi.DeleteMember)
		v1.GET("/member/company.config", registry.MemberApi.CompanyConfig)
		v1.GET("/member/dispatch.list", registry.MemberApi.DispatchList)

		// 无人机
		v1.GET("/drone.list", registry.DroneApi.List) // 无人机列表
		//v1.GET("/drone.detail", registry.DroneApi.Detail)             // 无人机详情
		//v1.GET("/drone.mgr_info", registry.DroneApi.MgrInfo)          // 无人机总体管理信息
		//v1.POST("/drone.update", registry.DroneApi.Update)            // 更新无人机
		//v1.POST("/drone.create", registry.DroneApi.Create)            // 创建无人机
		//v1.POST("/drone.delete", registry.DroneApi.Delete)            // 删除无人机
		//v1.POST("/drone.update_task", registry.DroneApi.UpdateTaskId) // 更新无人机绑定的任务id

		// 航线文件
		v1.GET("/wayline.list", registry.WaylineFileApi.List)      // 查询航线文件列表
		v1.POST("/wayline.delete", registry.WaylineFileApi.Delete) // 删除航线文件
		v1.POST("/wayline.create", registry.WaylineFileApi.Create) // 创建航线文件信息
		v1.POST("/wayline.update", registry.WaylineFileApi.Update) // 修改航线文件信息

		// 通用功能
		v1.GET("/common.get_weather", registry.CommonApi.GetWeather) // 获取天气

		// 文件上传相关
		v1.POST("/common_file_mgr.upload", registry.CommonFileMgrApi.Upload)
		v1.POST("/common_file_mgr.rename", registry.CommonFileMgrApi.Rename)
		v1.POST("/common_file_mgr.remove", registry.CommonFileMgrApi.Remove)

		// 通用算法
		v1.GET("/algorithm_model.list", registry.AlgorithmModelApi.List)      // 算法模型列表
		v1.GET("/algorithm_model.detail", registry.AlgorithmModelApi.Detail)  // 算法模型详情
		v1.POST("/algorithm_model.create", registry.AlgorithmModelApi.Create) // 创建算法模型
		v1.POST("/algorithm_model.delete", registry.AlgorithmModelApi.Delete) // 删除算法模型
		//v1.POST("/algorithm_model.invoke", registry.AlgorithmModelApi.Invoke)     // 算法模型调用
		v1.POST("/algorithm_model.rollback", registry.AlgorithmModelApi.Rollback)                  // 算法版本回滚
		v1.GET("/algorithm_model.get_online_version", registry.AlgorithmModelApi.GetOnlineVersion) // 根据算法id获取该算法的线上版本记录

		// 场景模型
		v1.GET("/algorithm_model_scenario.list", registry.AlgorithmModelScenarioApi.List) // 场景模型列表

		// 巡检任务
		v1.GET("/inspection_job.list", registry.InspectionJobApi.List)                                   // 巡检任务列表
		v1.GET("/inspection_job.detail", registry.InspectionJobApi.Detail)                               // 巡检任务详情
		v1.POST("/inspection_job.delete", registry.InspectionJobApi.Delete)                              // 删除巡检任务
		v1.POST("/inspection_job.create", registry.InspectionJobApi.Create)                              // 创建巡检任务
		v1.POST("/inspection_job.one_click_take", registry.InspectionJobApi.OneClickTakeoff)             // 一建起飞
		v1.POST("/inspection_job.update", registry.InspectionJobApi.Update)                              // 更新巡检任务
		v1.POST("/inspection_job.execute", registry.InspectionJobApi.Execute)                            // 立即执行已经创建的任务
		v1.GET("/inspection_job.calendar", registry.InspectionJobApi.JobCalendar)                        // 获取任务日历(月)
		v1.POST("/inspection_job.temporary_task", registry.InspectionJobApi.ExecuteTemporaryTask)        // 执行临时任务
		v1.POST("/inspection_job.create_with_facility", registry.InspectionJobApi.CreateJobWithFacility) // 创建设施巡检任务

		// 巡检记录
		v1.GET("/inspection_job_record.list", registry.InspectionJobRecordApi.List)               // 巡检记录列表
		v1.GET("/inspection_job_record.detail", registry.InspectionJobRecordApi.Detail)           // 巡检记录详情
		v1.POST("/inspection_job_record.delete", registry.InspectionJobRecordApi.Delete)          // 删除记录
		v1.POST("/inspection_job_record.flight_data", registry.InspectionJobRecordApi.FlightData) // 飞行数据
		v1.POST("/inspection_job_record.end", registry.InspectionJobRecordApi.EndJob)             // 手动结束业务任务
		v1.POST("/inspection_job_record.gen_report", registry.InspectionJobRecordApi.GenReport)   // 生成报告

		v1.POST("/wayline_job.return_home", registry.WaylineJobApi.ReturnHome)                   // 一键返航
		v1.POST("/wayline_job.update_job_status", registry.WaylineJobApi.UpdateJobStatus)        // 修改航线任务状态 暂停/恢复
		v1.POST("/wayline_job.cancel", registry.WaylineJobApi.CancelFlightTask)                  // 取消任务
		v1.POST("/wayline_job.execute_in_flight", registry.WaylineJobApi.InFlightWaylineDeliver) // 在空中下发航线

		// 航线管理
		v1.GET("/wayline_manage.list", registry.WaylineManageApi.List)      // 航线列表
		v1.GET("/wayline_manage.detail", registry.WaylineManageApi.Detail)  // 航线详情
		v1.POST("/wayline_manage.delete", registry.WaylineManageApi.Delete) // 删除航线
		v1.POST("/wayline_manage.create", registry.WaylineManageApi.Create) // 创建航线
		v1.POST("/wayline_manage.update", registry.WaylineManageApi.Update) // 更新航线
		v1.POST("/wayline_manage.upload", registry.WaylineManageApi.Upload) //上传航线文件
		v1.POST("/wayline_manage.parse", registry.WaylineManageApi.Parse)   // 解析航线

		// 设备
		v1.GET("/device.list", registry.DeviceApi.List) // 设备列表

		// 无人机飞手
		v1.GET("/drone_pilot.list", registry.DronePilotApi.List) // 列表

		// 机场-远程调试
		v1.POST("/control/debug.debug_mode_open", registry.DockDebugApi.DebugModeOpen)
		v1.POST("/control/debug.debug_mode_close", registry.DockDebugApi.DebugModeClose)
		v1.POST("/control/debug.supplement_light_open", registry.DockDebugApi.SupplementLightOpen)
		v1.POST("/control/debug.supplement_light_close", registry.DockDebugApi.SupplementLightClose)
		v1.POST("/control/debug.battery_maintenance_switch", registry.DockDebugApi.BatteryMaintenanceSwitch)
		v1.POST("/control/debug.air_conditioner_mode_switch", registry.DockDebugApi.AirConditionerModeSwitch)
		v1.POST("/control/debug.alarm_state_switch", registry.DockDebugApi.AlarmStateSwitch)
		v1.POST("/control/debug.battery_store_mode_switch", registry.DockDebugApi.BatteryStoreModeSwitch)
		v1.POST("/control/debug.device_reboot", registry.DockDebugApi.DeviceReboot)
		v1.POST("/control/debug.drone_open", registry.DockDebugApi.DroneOpen)
		v1.POST("/control/debug.drone_close", registry.DockDebugApi.DroneClose)
		v1.POST("/control/debug.device_format", registry.DockDebugApi.DeviceFormat)
		v1.POST("/control/debug.drone_format", registry.DockDebugApi.DroneFormat)
		v1.POST("/control/debug.cover_open", registry.DockDebugApi.CoverOpen)
		v1.POST("/control/debug.cover_close", registry.DockDebugApi.CoverClose)
		v1.POST("/control/debug.charge_open", registry.DockDebugApi.ChargeOpen)
		v1.POST("/control/debug.charge_close", registry.DockDebugApi.ChargeClose)
		v1.POST("/control/debug.sdr_work_mode_switch", registry.DockDebugApi.SDRWorkModeSwitch)
		v1.POST("/control/debug.putter_open", registry.DockDebugApi.PutterOpen)
		v1.POST("/control/debug.putter_close", registry.DockDebugApi.PutterClose)

		// 机场-drc
		v1.POST("/control/drc.enter", registry.DockDRCApi.DrcEnter) // 进入drc模式
		v1.POST("/control/drc.exit", registry.DockDRCApi.DrcExit)   // 退出drc模式
		v1.POST("/control/drc.cmd", registry.DockDRCApi.DrcCmd)     // 发送drc指令
		v1.POST("/control/drc.stop", registry.DockDRCApi.DrcStop)   // 停止drc指令

		// 机场-控制权
		v1.POST("/control/authority.flight", registry.DockControlApi.SeizeFlightAuthority)
		v1.POST("/control/authority.payload", registry.DockControlApi.SeizePayloadAuthority)

		// 机场-负载控制
		v1.POST("/control/payload.cmd", registry.DockControlApi.PayloadCmd)
		v1.POST("/control/payload/speaker.tts_set", registry.PayloadApi.SpeakerTtsPlayStart) // TTS喊话
		v1.POST("/control/payload/light.mode_set", registry.PayloadApi.LightModeSet)         // 设置灯光模式

		// 机场-指令飞行任务控制
		v1.POST("/control/jobs.takeoff_to_point", registry.DockControlApi.TakeoffToPoint)
		v1.POST("/control/jobs.fly_to_point", registry.DockControlApi.FlyToPoint)
		v1.POST("/control/jobs.fly_to_point_stop", registry.DockControlApi.FlyToPointStop)

		// 机场-云台快捷控制
		v1.POST("/control/gimbal.one_key_down_shoot", registry.DockControlApi.OneKeyDownShoot) // 一键俯拍
		v1.POST("/control/gimbal.one_key_restore", registry.DockControlApi.OneKeyRestore)      // 一键恢复

		// 机场-远程控制
		v1.POST("/dock/control_remote/light.calibration", registry.DockRemoteControlApi.LightCalibration)           // 探照灯云台校准
		v1.POST("/dock/control_remote/light.mode_set", registry.DockRemoteControlApi.LightModeSet)                  // 探照灯模式设置
		v1.POST("/dock/control_remote/light.fine_tuning_set", registry.DockRemoteControlApi.LightFineTuningSet)     // 探照灯设置
		v1.POST("/dock/control_remote/light.brightness_set", registry.DockRemoteControlApi.LightBrightnessSet)      // 探照灯亮度设置
		v1.POST("/dock/control_remote/speaker.tts_play_start", registry.DockRemoteControlApi.SpeakerTtsPlayStart)   // DRC喊话器播放TTS文本
		v1.POST("/dock/control_remote/speaker.play_stop", registry.DockRemoteControlApi.SpeakerPlayStop)            // DRC喊话器停止播放
		v1.POST("/dock/control_remote/speaker.replay", registry.DockRemoteControlApi.SpeakerReplay)                 // DRC喊话器重新播放
		v1.POST("/dock/control_remote/speaker.play_volume_set", registry.DockRemoteControlApi.SpeakerPlayVolumeSet) // DRC喊话器设置音量
		v1.POST("/dock/control_remote/speaker.play_mode_set", registry.DockRemoteControlApi.SpeakerPlayModeSet)     // DRC喊话器设置播放模式
		v1.POST("/dock/control_remote/speaker.tts_set", registry.DockRemoteControlApi.SpeakerTtsSet)                // DRC喊话器TTS喊话设置

		v1.POST("/dock/control_remote.drc_force_landing", registry.DockRemoteControlApi.DrcForceLanding)         // 强制降落 无视避障强制降落
		v1.POST("/dock/control_remote.drc_emergency_landing", registry.DockRemoteControlApi.DrcEmergencyLanding) // 紧急降落

		// 机场-psdk
		v1.POST("/dock/psdk/speaker.tts_play", registry.DockPsdkApi.SpeakerTtsPlay)          // 喊话器播放TTS文本
		v1.POST("/dock/psdk/speaker.audio_play", registry.DockPsdkApi.SpeakerAudioPlay)      // 喊话器播放音频文件
		v1.POST("/dock/psdk/speaker.stop", registry.DockPsdkApi.SpeakerStop)                 // 喊话器停止播放
		v1.POST("/dock/psdk/speaker.restart", registry.DockPsdkApi.SpeakerRestart)           // 喊话器重新播放
		v1.POST("/dock/psdk/speaker.set_volume", registry.DockPsdkApi.SpeakerSetVolume)      // 喊话器设置音量
		v1.POST("/dock/psdk/speaker.set_play_mode", registry.DockPsdkApi.SpeakerSetPlayMode) // 喊话器设置播放模式

		// 直播
		v1.POST("/live.start", registry.LiveStreamApi.LiveStart)
		v1.POST("/live.stop", registry.LiveStreamApi.LiveStop)
		v1.POST("/live.set_quality", registry.LiveStreamApi.LiveSetQuality)
		v1.POST("/live.lens_change", registry.LiveStreamApi.LiveLensChange)

		v1.POST("/live/drone.restart", registry.LiveStreamApi.ReStartDroneLive)
		v1.POST("/live/drone.capacity", registry.LiveStreamApi.LiveCapacity)

		v1.POST("/live/dock.restart", registry.LiveStreamApi.DockLiveStart)

		// 驾驶舱
		v1.GET("/cockpit/flight.flight_resources", registry.CockpitFlightApi.FlightResources)                // 飞行资源
		v1.GET("/cockpit/flight.device_overview", registry.CockpitFlightApi.DeviceOverview)                  // 设备列表
		v1.GET("/cockpit/flight.flight_device_info", registry.CockpitFlightApi.FlightDeviceInfo)             // 飞行设备信息
		v1.GET("/cockpit/flight.flight_device_coordinate", registry.CockpitFlightApi.FlightDeviceCoordinate) // 飞行设备位置
		v1.GET("/cockpit/flight.flight_condition_data", registry.CockpitFlightApi.FlightConditionData)       // 飞行条件参数
		v1.POST("/cockpit/flight.add_fence", registry.FenceApi.Add)                                          //添加电子围栏
		v1.GET("/cockpit/flight.get_fence", registry.FenceApi.List)
		v1.POST("/cockpit/flight.delete_fence", registry.FenceApi.Delete) //获取电子围栏

		v1.POST("/cockpit/warn.create", registry.CockpitFlightApi.CreateWarn)                       // 创建告警
		v1.POST("/cockpit/warn.create_take_photo", registry.CockpitFlightApi.CreateWarnByTakePhoto) // 根据手动拍照来创建告警

		v1.GET("/cockpit/task.list", registry.CockpitFlightApi.TaskList)        // 任务列表
		v1.GET("/cockpit/task.monitor", registry.CockpitFlightApi.TaskMonitor)  // 任务监控
		v1.POST("/cockpit/dock.monitor", registry.CockpitFlightApi.DockMonitor) // 机场监控

		v1.GET("/cockpit/drone.flight_info", registry.CockpitFlightApi.DroneFlightInfo) // 无人机飞行参数
		v1.POST("/cockpit/drone.take_over", registry.CockpitFlightApi.DroneTakeOver)    // 无人机接管

		v1.POST("/cockpit/warn_record.create", registry.CockpitFlightApi.CreateWarnRecord) // 创建告警记录
		v1.GET("/cockpit/warn_record.latest", registry.CockpitFlightApi.LatestWarnRecord)  // 最新作业告警记录

		v1.GET("/cockpit/trajectory.overview", registry.CockpitFlightApi.TrajectoryOverview) // 当日任务轨迹总览

		// 工作台
		v1.GET("/work_bench.overview", registry.WorkBenchApi.Overview)
		v1.GET("/work_bench.resource", registry.WorkBenchApi.Resource)
		v1.GET("/work_bench.task_logs", registry.WorkBenchApi.TaskLogs)

		// 告警记录
		v1.GET("/warn_record.list", registry.JobWarnRecordApi.List)
		v1.GET("/warn_record.overview", registry.JobWarnRecordApi.Overview)
		v1.POST("/warn_record.update", registry.JobWarnRecordApi.Update)

		// 告警记录处理
		v1.POST("/warn_record_handle.handle", registry.JobWarnRecordHandleApi.Handle) // 处理告警记录
		v1.POST("/warn_record_handle.update", registry.JobWarnRecordHandleApi.Update) // 更新处理结果
		v1.GET("/warn_record_handle.detail", registry.JobWarnRecordHandleApi.Detail)  // 根据告警记录ID查询告警记录处理结果

		// 告警
		v1.GET("/warn.list", registry.JobWarnApi.List)
		v1.POST("/warn.model_check", registry.JobWarnApi.ModelCheck) // 大模型检测
		v1.GET("/warn.overview", registry.JobWarnApi.Overview)
		v1.GET("/warn.by_admin", registry.JobWarnApi.ListByAdmin) // 移动端管理人员获取告警列表
		v1.GET("/warn.by_user", registry.JobWarnApi.ListByUser)   // 移动端处理人员获取告警列表
		v1.POST("/warn.sender", registry.JobWarnApi.Sender)
		v1.POST("/warn.update_status", registry.JobWarnApi.UpdateStatus) //更新状态
		v1.POST("/warn.delete", registry.JobWarnApi.Delete)

		// 告警派遣
		v1.POST("/warn_dispatch.dispatch", registry.JobWarnDispatchApi.Dispatch) // 派遣告警
		v1.POST("/warn_dispatch.accept", registry.JobWarnDispatchApi.Accept)     // 接取告警
		v1.POST("/warn_dispatch.complete", registry.JobWarnDispatchApi.Complete) // 完成告警

		// 告警工单
		v1.GET("/warn_work_order.list", registry.WarnWorkOrderApi.ListWorkOrder) // 工单列表
		v1.GET("/warn_work_order.detail", registry.WarnWorkOrderApi.Detail)      // 工单详情
		v1.POST("/warn_work_order.delete", registry.WarnWorkOrderApi.Delete)     // 删除工单

		// 地下信息
		v1.GET("/underground_info.list", registry.UndergroundInfoApi.List)      // 地下信息列表
		v1.GET("/underground_info.detail", registry.UndergroundInfoApi.Detail)  // 地下信息详情
		v1.POST("/underground_info.create", registry.UndergroundInfoApi.Create) // 创建地下信息
		v1.POST("/underground_info.update", registry.UndergroundInfoApi.Update) // 更新地下信息
		v1.POST("/underground_info.delete", registry.UndergroundInfoApi.Delete) // 删除地下信息

		// 设备管理
		v1.GET("/device_manage.list", registry.DeviceManageApi.List)      // 设备列表
		v1.GET("/device_manage.detail", registry.DeviceManageApi.Detail)  // 设备详情
		v1.POST("/device_manage.create", registry.DeviceManageApi.Create) // 创建设备
		v1.POST("/device_manage.update", registry.DeviceManageApi.Update) // 更新设备信息
		v1.POST("/device_manage.delete", registry.DeviceManageApi.Delete) // 删除设备

		// 摄像头
		v1.GET("/camera.test", registry.CameraApi.PresetPoint) // 演示测试
	}

	v2 := apix.NewRouterGroup(router.Group("/v2/app", middleware.AuthMiddleware(token.LoginRoleUser)))
	{
		// 添加oss鉴权机制后 更新关于航线文件v2接口
		v2.POST("/wayline.delete", registry.WaylineFileApi.DeleteV2) // 删除航线文件
		v2.POST("/wayline.create", registry.WaylineFileApi.CreateV2) // 创建航线文件信息
		v2.POST("/wayline.update", registry.WaylineFileApi.UpdateV2) // 修改航线文件信息

		v2.GET("/cockpit/task.list", registry.CockpitFlightApi.TaskListV2) // 任务列表

		v2.GET("/work_bench.overview", registry.WorkBenchApi.OverviewV2)
	}
}
