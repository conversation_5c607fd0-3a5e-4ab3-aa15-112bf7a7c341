package utils

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strconv"
	"time"
)

const mySecret = "f6fdffe48c908deb0f4c3bd36c032e72"

// md5Params 生成带签名的参数
func md5Params(params map[string]string) map[string]string {
	// 添加当前时间戳
	params["t"] = strconv.FormatInt(time.Now().Unix(), 10)

	// 获取并排序所有键
	keys := make([]string, 0, len(params)+1)
	for k := range params {
		keys = append(keys, k)
	}
	keys = append(keys, "secret")
	sort.Strings(keys)

	// 构建签名字符串
	str := ""
	for _, key := range keys {
		if key == "token" {
			continue
		}
		if str != "" {
			str += "&"
		}
		if key == "secret" {
			str += key + "=" + mySecret
		} else {
			str += key + "=" + params[key]
		}
	}

	// 计算MD5
	hasher := md5.New()
	hasher.Write([]byte(str))
	params["token"] = hex.EncodeToString(hasher.Sum(nil))

	fmt.Printf("md5: %s %s\n", str, params["token"])
	return params
}

func CameraCall(url string, args map[string]string) {
	newArgs := md5Params(args)
	reqUrl := url
	i := 0
	for k, v := range newArgs {
		if i == 0 {
			reqUrl += fmt.Sprintf("?%s=%s", k, v)
		} else {
			reqUrl += fmt.Sprintf("&%s=%s", k, v)
		}
		i++
	}

	fmt.Println("reqUrl	", reqUrl)
	body, err := http.Get(reqUrl)
	if err != nil {
		fmt.Println("http.Get error", err)
		return
	}
	defer body.Body.Close()
	bodyBytes, err := io.ReadAll(body.Body)
	if err != nil {
		fmt.Println("io.ReadAll error", err)
		return
	}
	fmt.Println("bodyBytes", string(bodyBytes))
}

func PTZControl() {
	//水平 19*360 = 6840 步长
	// 80*20
	// speed 4 17 步长 361次一圈
	// speed 5 16 步长 360次一圈
	args := map[string]string{
		"value":   "r",
		"speed":   "5",
		"channel": "1",
		"stop":    "1",
		"steps":   "17",
	}
	url := "https://live.xstrive.com/14d271126fa51f9dfdc05ddd5d87fc20/xsw/api/ptz/control"
	for i := 0; i < 360; i++ {
		CameraCall(url, args)
		time.Sleep(4 * time.Second)
	}
}
func PresetPoint() {
	args := map[string]string{
		"value": "2",
		"point": "1",
	}
	url := "https://live.xstrive.com/14d271126fa51f9dfdc05ddd5d87fc20/xsw/api/ptz/presetPoint"
	CameraCall(url, args)
	time.Sleep(6 * time.Second)
	args2 := map[string]string{
		"value": "2",
		"point": "2",
	}
	CameraCall(url, args2)

	time.Sleep(6 * time.Second)
	args3 := map[string]string{
		"value":   "f",
		"speed":   "20",
		"channel": "1",
		"stop":    "1",
		"steps":   "360",
	}
	url2 := "https://live.xstrive.com/14d271126fa51f9dfdc05ddd5d87fc20/xsw/api/ptz/control"
	CameraCall(url2, args3)

}
